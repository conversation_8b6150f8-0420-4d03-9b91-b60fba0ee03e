//
//  OrderViewController.m
//  BRZY
//
//  Created by YYKKJ on 2017/11/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "OrderViewController.h"

#import "OrderCell.h"
#import "CompleteTotalOrGapCell.h"
#import "CompleteDetailCell.h"
#import "CompleteTotalCell.h"
#import "CompleteModel.h"
#import "MedicatedInfoViewController.h"
#import "UIScrollView+EmptyDataSet.h"
#import "MJRefresh.h"
#import "BRAlertView.h"
#import "Reachability.h"
#import "BRNoDataView.h"
#import "BRPresNoticeView.h"
#import "WXApi.h"
#import "WzdWebViewController.h"

@interface OrderViewController ()<UITableViewDelegate,UITableViewDataSource,UIScrollViewDelegate,UITextFieldDelegate,DZNEmptyDataSetSource,DZNEmptyDataSetDelegate,UIGestureRecognizerDelegate>
{
    NSInteger currentPage;
    NSInteger totalPageSize;
    int         time; //用来计时
    BOOL isRemind;
    NSString *backStr;
    NSString *lastYearMouth;
    NSInteger buttonTag;
}
#pragma mark - 待付款 已完成 已过期 全部订单按钮
@property (nonatomic, strong) UIButton                  *waitingPaymentButton;
@property (nonatomic, strong) UIButton                  *finishedButton;
@property (nonatomic, strong) UIButton                  *unfinishedButton;
@property (nonatomic, strong) UIButton                  *allButton;
@property (nonatomic, strong) UIImageView               *lineImageView;
@property (nonatomic, strong) UIScrollView              *orderScrollView;
#pragma mark - 待付款 已完成 已过期 全部订单tableView
@property (nonatomic, strong) UIView                    *topButtonView;
@property (nonatomic, strong) UITableView               *waitingPaymentTab;
@property (nonatomic, strong) UITableView               *finishedTab;
@property (nonatomic, strong) UITableView               *unfinishedTab;
@property (nonatomic, strong) UITableView               *allTab;
#pragma mark - 待付款 已完成 已过期 全部订单数据
@property (nonatomic, strong) NSMutableArray            *waitingPaymentArray;
@property (nonatomic, strong) NSMutableArray            *finishedArray;
@property (nonatomic, strong) NSMutableArray            *unfinishedArray;
@property (nonatomic, strong) NSMutableArray            *allArray;
#pragma mark - 显示用药订单获取验证码弹框
@property (nonatomic, strong) UIView                    *backview;
@property (nonatomic, strong) UIView                    *orderView;
@property (nonatomic, strong) UITextField               *orderTextField;
@property (strong, nonatomic) UIButton                  *getAuthCodeBtn;
#pragma mark - 显示隐藏按钮
@property (strong, nonatomic) UIButton                  *button;

@property (strong, nonatomic) MJRefreshAutoNormalFooter *waitingFooter;
@property (strong, nonatomic) MJRefreshAutoNormalFooter *finishedFooter;
@property (strong, nonatomic) MJRefreshAutoNormalFooter *unfinishedFooter;
@property (strong, nonatomic) MJRefreshAutoNormalFooter *allFooter;

@property (nonatomic, strong) Reachability              *reachability;
@property (nonatomic, strong) BRNoDataView              *noDataView;
@property (nonatomic, strong) BRPresNoticeView          *orderNoticeView;
@end

@implementation OrderViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"用药订单";
    [self showNavBackItem];
    
    //去掉navigationbar底部线条
    self.navigationController.navigationBar.clipsToBounds = NO;
    
    [self showOrHiddenButton];
    
    [self showUI];
    
    [self creatRefresh];
    
    _waitingPaymentArray = [[NSMutableArray alloc] init];
    _finishedArray = [[NSMutableArray alloc] init];
    _unfinishedArray = [[NSMutableArray alloc] init];
    _allArray = [[NSMutableArray alloc] init];
    
    currentPage = 1;
    lastYearMouth = @"";
    time = 60;
    
    [self hearderViewSegmentdedUI:_payState];
    
    if (_payState == 102 && [_showAndHidden isEqualToString:@"1"]) {
        [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
    }
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if(self.orderScrollView.contentOffset.x == 0){
        if (_payState == 102 && [_showAndHidden isEqualToString:@"1"]) {
            [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
        }
    }
}

#pragma mark - 隐藏显示按钮
- (void)showOrHiddenButton
{
    _button = [UIButton buttonWithType:UIButtonTypeCustom];
    //设置图片
    UIImage *imageForButton;
    NSString *buttonTitleStr;
    if ([_showAndHidden isEqualToString:@"1"]) {
        
        imageForButton = [UIImage imageNamed:@"order_yincangdingdan"];
        buttonTitleStr = @"隐藏";
        _button.selected = NO;
        
    } else  if ([_showAndHidden isEqualToString:@"2"]) {
        
        imageForButton = [UIImage imageNamed:@"order_xianshidingdan"];
        buttonTitleStr = @"显示";
        _button.selected = YES;
    }
    //设置文字
    [_button setImage:imageForButton forState:UIControlStateNormal];
    [_button setTitle:buttonTitleStr forState:UIControlStateNormal];
    [_button setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
    _button.titleLabel.font = kFontRegular(15);
    [_button addTarget:self action:@selector(showOrHiddenButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    CGSize buttonTitleLabelSize = [buttonTitleStr sizeWithAttributes:@{NSFontAttributeName:_button.titleLabel.font}]; //文本尺寸
    CGSize buttonImageSize = imageForButton.size;   //图片尺寸
    _button.frame = CGRectMake(0,0,
                               buttonImageSize.width + buttonTitleLabelSize.width,
                               buttonImageSize.height);
    UIBarButtonItem *barButtonItem = [[UIBarButtonItem alloc] initWithCustomView:_button];
    self.navigationItem.rightBarButtonItem = barButtonItem;
}
#pragma mark - 隐藏显示按钮点击事件
- (void)showOrHiddenButtonClick:(UIButton *)button
{
    
    if (button.selected) {
        if ([backStr isEqualToString:@"1"]) {
            _backview.hidden = NO;
        } else {
            [self getVerificationCode];
        }
        
    } else {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"隐藏" forState:UIControlStateNormal];
        [alertView showAlertViewWithCancelButton:@"是否隐藏您的用药订单？"  completion:^(BOOL isOk) {
            
            [alertView close];
            if (isOk) {
                [self.waitingPaymentTab setContentOffset:CGPointMake(0,0) animated:NO];
                [self.finishedTab setContentOffset:CGPointMake(0,0) animated:NO];
                [self.unfinishedTab setContentOffset:CGPointMake(0,0) animated:NO];
                [self.allTab setContentOffset:CGPointMake(0,0) animated:NO];
                [button setImage:[UIImage imageNamed:@"order_xianshidingdan"] forState:UIControlStateNormal];
                [button setTitle:@"显示" forState:UIControlStateNormal];
                button.selected = YES;
                _orderScrollView.scrollEnabled = NO;
                [self updateTheDisplayStateValue:@"2" smsCode:@""];
                _showAndHidden = @"2";
                _topButtonView.hidden = YES;
                if (_waitingPaymentButton.selected) {
                    
                    [_waitingPaymentArray removeAllObjects];
                    self.waitingPaymentTab.mj_footer.hidden = YES;
                    [_waitingPaymentTab reloadData];
                    
                } else if (_finishedButton.selected) {
                    
                    [_finishedArray removeAllObjects];
                    self.finishedTab.mj_footer.hidden = YES;;
                    [_finishedTab reloadData];
                    
                }else if (_unfinishedButton.selected) {
                    
                    [_unfinishedArray removeAllObjects];
                    self.unfinishedTab.mj_footer.hidden = YES;;
                    [_unfinishedTab reloadData];
                    
                }else if (_allButton.selected) {
                    
                    [_allArray removeAllObjects];
                    self.allTab.mj_footer.hidden = YES;;
                    [_allTab reloadData];
                    
                }
            }
        }];
    }
}
#pragma mark - 显示用药订单弹框
- (void)getVerificationCode
{
    backStr = @"1";
    
    _backview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    _backview.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    _backview.tag = 321;
    [[UIApplication sharedApplication].keyWindow addSubview:_backview];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBackground:)];
    tap.delegate = self;
    [_backview addGestureRecognizer:tap];
    
    _orderView = [[UIView alloc]initWithFrame:CGRectMake(30, (kScreenHeight-300)/2-30, kScreenWidth - 60, 300)];
    _orderView.backgroundColor = [UIColor whiteColor];
    _orderView.layer.cornerRadius = 10;
    [_backview addSubview:_orderView];
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 30, _orderView.frame.size.width, 18)];
    titleLabel.text = @"显示用药订单";
    titleLabel.textColor = [UIColor br_textBlueColor];
    titleLabel.font = kFontRegular(17);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [_orderView addSubview:titleLabel];
    
    UITextField *telTF = [[UITextField alloc]initWithFrame:CGRectMake(30, 84, _orderView.frame.size.width-60,40)];
    telTF.layer.cornerRadius = 21;
    telTF.text = [UserManager shareInstance].getTelephone;
    telTF.font = kFontLight(16);
    telTF.backgroundColor = [UIColor br_backgroundColor];
    telTF.leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 0)];
    telTF.leftViewMode = UITextFieldViewModeAlways;
    telTF.enabled = NO;
    [_orderView addSubview:telTF];
    
    _orderTextField = [[UITextField alloc]initWithFrame:CGRectMake(30, 144, _orderView.frame.size.width-60,40)];
    _orderTextField.layer.cornerRadius = 21;
    _orderTextField.delegate = self;
    _orderTextField.returnKeyType = UIReturnKeyDone;
    _orderTextField.keyboardType = UIKeyboardTypeNumberPad;
    if (isiPhone5) {
        _orderTextField.placeholder = @"验证码";
    } else {
        _orderTextField.placeholder = @"请填写验证码";
    }
    
    _orderTextField.font = kFontLight(16);
    _orderTextField.backgroundColor = [UIColor br_backgroundColor];
    _orderTextField.leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 0)];
    _orderTextField.leftViewMode = UITextFieldViewModeAlways;
    [_orderView addSubview:_orderTextField];
    
    _getAuthCodeBtn = [[UIButton alloc] initWithFrame:CGRectMake(_orderView.frame.size.width-140, 149, 100, 30)];
    _getAuthCodeBtn.layer.cornerRadius = 15;
    _getAuthCodeBtn.layer.borderWidth = 0.5;
    _getAuthCodeBtn.clipsToBounds = YES;
    _getAuthCodeBtn.layer.borderColor = [UIColor colorWithHex:0xb1b1b1].CGColor;
    _getAuthCodeBtn.titleLabel.font = kFontLight(13);
    [_getAuthCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
    [_getAuthCodeBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [_getAuthCodeBtn setBackgroundImage:[Utils createImageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [_getAuthCodeBtn setBackgroundImage:[Utils createImageWithColor:[UIColor clearColor]] forState:UIControlStateDisabled];
    [_getAuthCodeBtn addTarget:self action:@selector(verificationCodeButtonClick) forControlEvents:UIControlEventTouchUpInside];
    [_orderView addSubview:_getAuthCodeBtn];
    
    
    UIButton *cancelButton = [[UIButton alloc]initWithFrame:CGRectMake(30, _orderView.frame.size.height-76, (_orderView.frame.size.width-75)/2, 40)];
    [cancelButton setTitle:@"取消" forState:UIControlStateNormal];
    [cancelButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [cancelButton addTarget:self action:@selector(senderClick:) forControlEvents:UIControlEventTouchUpInside];
    cancelButton.tag = 200;
    cancelButton.layer.borderColor = [UIColor br_disableBgColor].CGColor;
    cancelButton.layer.borderWidth = 0.5;
    cancelButton.layer.cornerRadius = 5;
    cancelButton.clipsToBounds = YES;
    cancelButton.titleLabel.font = kFontLight(16);
    [_orderView addSubview:cancelButton];
    
    UIButton *saveButton = [[UIButton alloc]initWithFrame:CGRectMake(_orderView.frame.size.width/2+15, _orderView.frame.size.height-76, (_orderView.frame.size.width-75)/2, 40)];
    [saveButton setTitle:@"显示" forState:UIControlStateNormal];
    [saveButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [saveButton setBackgroundImage:[UIImage imageWithColor:[UIColor br_textBlueColor]] forState:UIControlStateNormal];
    [saveButton addTarget:self action:@selector(senderClick:) forControlEvents:UIControlEventTouchUpInside];
    saveButton.tag = 201;
    saveButton.layer.cornerRadius = 5;
    saveButton.clipsToBounds = YES;
    saveButton.titleLabel.font = kFontLight(16);
    [_orderView addSubview:saveButton];
}
#pragma mark - 显示用药订单弹框显示取消点击事件
- (void)senderClick:(UIButton *)sender
{
    if (sender.tag == 200) {
        _backview.hidden = YES;
        [_orderTextField resignFirstResponder];
    } else {
        
        if (_orderTextField.text.length == 0) {
            
            [_orderView makeToast:@"请输入验证码" duration:2 position:CSToastPositionCenter];
            return;
        }
        [self updateTheDisplayStateValue:@"1" smsCode:_orderTextField.text];
    }
}
#pragma mark - UIGestureRecognizerDelegate
- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch{
    
    if (![touch.view isDescendantOfView:self.orderView]) {//区分点击的哪个view
        _backview.hidden = YES;
        return NO;
    }
    return YES;
}

- (void)tapBackground:(UITapGestureRecognizer *)tap
{
    
}
#pragma mark - 获取验证码
- (void)verificationCodeButtonClick {
    
    //防止重复点击
    self.getAuthCodeBtn.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.getAuthCodeBtn.userInteractionEnabled = YES;
    });
    
    [self requestVerification];
}
#pragma mark -获取验证码
- (void)requestVerification {
    
    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    //获取验证码
    NSDictionary *dict = @{@"method_code":@"000031",
                           @"type":@"1",
                           @"tel":[UserManager shareInstance].getTelephone,
                           @"optType":@"1"
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        [_orderTextField becomeFirstResponder];
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            [_orderView makeToast:@"验证码已发送" duration:2 position:CSToastPositionCenter];
            
            [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:nil];
            NSTimer *timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(timerAdvanced:) userInfo:nil repeats:YES];
            [[NSRunLoop currentRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
            
        } else {
            [_orderView makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
    }];
    
}
#pragma mark - 倒计时响应事件
- (void)timerAdvanced:(NSTimer *)timer {
    
    time--;
    
    if (time == 0) {
        
        [timer invalidate];
        
        time = 60;
        
        [_getAuthCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
        [_getAuthCodeBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _getAuthCodeBtn.enabled = YES;
        
    }
    else {
        
        [_getAuthCodeBtn setTitle:[NSString stringWithFormat:@"重新发送(%ds)",time] forState:UIControlStateNormal];
        [_getAuthCodeBtn setTitleColor:[UIColor lightGrayColor] forState:UIControlStateNormal];
        _getAuthCodeBtn.enabled = NO;
        
    }
    
}
#pragma mark - 待付款 已完成 已过期 全部订单按钮及tableView
- (void)showUI
{
    _topButtonView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 45)];
    _topButtonView.backgroundColor = [UIColor whiteColor];
    if ([_showAndHidden isEqualToString:@"1"]) {
        _topButtonView.hidden = NO;
    } else {
        _topButtonView.hidden = YES;
    }
    [self.view addSubview:_topButtonView];
    
    _waitingPaymentButton = [[UIButton alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth/4, 45)];
    _waitingPaymentButton.tag = 102;
    [_waitingPaymentButton setTitle:@"待付款" forState:UIControlStateNormal];
    [_waitingPaymentButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_waitingPaymentButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_waitingPaymentButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_topButtonView addSubview:_waitingPaymentButton];
    
    _finishedButton = [[UIButton alloc]initWithFrame:CGRectMake(kScreenWidth/4, 0, kScreenWidth/4, 45)];
    _finishedButton.tag = 101;
    [_finishedButton setTitle:@"已完成" forState:UIControlStateNormal];
    [_finishedButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_finishedButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_finishedButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_topButtonView addSubview:_finishedButton];
    
    _unfinishedButton = [[UIButton alloc]initWithFrame:CGRectMake(kScreenWidth/4*2, 0, kScreenWidth/4, 45)];
    _unfinishedButton.tag = 107;
    [_unfinishedButton setTitle:@"已过期" forState:UIControlStateNormal];
    [_unfinishedButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_unfinishedButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_unfinishedButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_topButtonView addSubview:_unfinishedButton];
    
    _allButton = [[UIButton alloc]initWithFrame:CGRectMake(kScreenWidth/4*3, 0, kScreenWidth/4, 45)];
    _allButton.tag = 103;
    [_allButton setTitle:@"全部订单" forState:UIControlStateNormal];
    [_allButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_allButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_allButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_topButtonView addSubview:_allButton];
    
    UIImageView *lineImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 44, kScreenWidth, 0.7)];
    lineImageView.backgroundColor = [UIColor br_insideDivisionLineColor];
    [_topButtonView addSubview:lineImageView];
    
    _lineImageView = [[UIImageView alloc]initWithFrame:CGRectMake((kScreenWidth/4-70)/2, 42, 70, 3)];
    _lineImageView.backgroundColor = [UIColor br_textBlueColor];
    _lineImageView.layer.cornerRadius = 2;
    [_topButtonView addSubview:_lineImageView];
    
    
    _orderScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 45, kScreenWidth, kScreenHeight-45-(isiPhoneX ? 96 : 64)-kTabbarSafeBottomMargin)];
    _orderScrollView.contentSize = CGSizeMake(kScreenWidth*4, kScreenHeight-45-(isiPhoneX ? 96 : 64)-kTabbarSafeBottomMargin);
    _orderScrollView.pagingEnabled = YES;
    _orderScrollView.backgroundColor = [UIColor clearColor];
    _orderScrollView.delegate = self;
    if ([_showAndHidden isEqualToString:@"2"]) {
        _orderScrollView.scrollEnabled = NO;
    } else {
        _orderScrollView.scrollEnabled = YES;
    }
    [self.view addSubview:_orderScrollView];
    
    _waitingPaymentTab = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, _orderScrollView.frame.size.height) style:UITableViewStylePlain];
    _waitingPaymentTab.delegate = self;
    _waitingPaymentTab.dataSource = self;
    _waitingPaymentTab.emptyDataSetSource = self;
    _waitingPaymentTab.emptyDataSetDelegate = self;
    [_waitingPaymentTab setSeparatorColor:[UIColor br_insideDivisionLineColor]];
    _waitingPaymentTab.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    _waitingPaymentTab.backgroundColor = [UIColor br_backgroundColor];
    [_orderScrollView addSubview:_waitingPaymentTab];
    
    _finishedTab = [[UITableView alloc] initWithFrame:CGRectMake(kScreenWidth, 0, kScreenWidth, _orderScrollView.frame.size.height) style:UITableViewStylePlain];
    _finishedTab.delegate = self;
    _finishedTab.dataSource = self;
    _finishedTab.emptyDataSetDelegate = self;
    _finishedTab.emptyDataSetSource = self;
    [_finishedTab setSeparatorColor:[UIColor br_insideDivisionLineColor]];
    _finishedTab.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    _finishedTab.backgroundColor = [UIColor br_backgroundColor];
    [_orderScrollView addSubview:_finishedTab];
    
    
    //注册cell
    [_finishedTab registerClass:[CompleteDetailCell class] forCellReuseIdentifier:@"CompleteDetailCell"];
    
    
    
    
    _orderNoticeView = [[BRPresNoticeView alloc] init];
    _orderNoticeView.noticeStr = @"点击进入用药详情，支持复制药方。";
    _orderNoticeView.hidden = YES;
    [_orderScrollView addSubview:_orderNoticeView];
    
    __weak OrderViewController *weakSelf = self;
    [_orderNoticeView mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.top.equalTo(self.orderScrollView.mas_top).offset(0);
        make.left.equalTo(self.finishedTab.mas_right).offset(0);
        make.width.mas_equalTo(kScreenWidth);
        make.height.mas_equalTo(0); // 初始状态为隐藏，高度为0
        
    }];
    
    _orderNoticeView.closeButtonBlock = ^ {
        
        [weakSelf hiddenNoticeView];
        
    };
    
    _unfinishedTab = [[UITableView alloc] init];
    _unfinishedTab.delegate = self;
    _unfinishedTab.dataSource = self;
    _unfinishedTab.emptyDataSetSource = self;
    _unfinishedTab.emptyDataSetDelegate = self;
    [_unfinishedTab setSeparatorColor:[UIColor br_insideDivisionLineColor]];
    _unfinishedTab.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    _unfinishedTab.backgroundColor = [UIColor br_backgroundColor];
    [_orderScrollView addSubview:_unfinishedTab];
    
    [_unfinishedTab mas_makeConstraints:^(MASConstraintMaker *make) {
        
        [self.orderNoticeView layoutIfNeeded];
        
        make.top.equalTo(self.orderNoticeView.mas_bottom).offset(0);
        make.left.equalTo(self.finishedTab.mas_right).offset(0);
        make.width.mas_equalTo(kScreenWidth);
        make.height.mas_equalTo(_orderScrollView.frame.size.height-_orderNoticeView.frame.size.height);
        
    }];
    
    _allTab = [[UITableView alloc] initWithFrame:CGRectMake(kScreenWidth*3, 0, kScreenWidth, _orderScrollView.frame.size.height) style:UITableViewStylePlain];
    _allTab.delegate = self;
    _allTab.dataSource = self;
    _allTab.emptyDataSetSource = self;
    _allTab.emptyDataSetDelegate = self;
    [_allTab setSeparatorColor:[UIColor br_insideDivisionLineColor]];
    _allTab.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    _allTab.backgroundColor = [UIColor br_backgroundColor];
    [_orderScrollView addSubview:_allTab];
}
#pragma mark - 待付款 已完成 已过期 全部订单按钮切换
- (void)buttonClick:(UIButton *)button
{
    if (buttonTag != button.tag) {
        [self hearderViewSegmentdedUI:button.tag];
    }
    buttonTag = button.tag;
}
#pragma mark - 待付款 已完成 已过期 全部订单切换响应事件
- (void)hearderViewSegmentdedUI:(NSInteger)tag
{
    buttonTag = tag;
    if (tag == 102) {
        
        currentPage = 1;
        _waitingPaymentButton.selected = YES;
        _finishedButton.selected = NO;
        _unfinishedButton.selected = NO;
        _allButton.selected = NO;
        _lineImageView.left = (kScreenWidth/4-70)/2;
        _orderScrollView.contentOffset = CGPointMake(0, 0);
        self.waitingPaymentTab.mj_footer.state = MJRefreshStateIdle;
        
    } else if (tag == 101) {
        
        currentPage = 1;
        lastYearMouth = @"";
        _waitingPaymentButton.selected = NO;
        _finishedButton.selected = YES;
        _unfinishedButton.selected = NO;
        _allButton.selected = NO;
        _lineImageView.left = kScreenWidth/4+((kScreenWidth/4-70)/2);
        _orderScrollView.contentOffset = CGPointMake(kScreenWidth, 0);
        self.finishedTab.mj_footer.state = MJRefreshStateIdle;
        
    } else if (tag == 107) {
        
        currentPage = 1;
        _waitingPaymentButton.selected = NO;
        _finishedButton.selected = NO;
        _unfinishedButton.selected = YES;
        _allButton.selected = NO;
        _lineImageView.left = kScreenWidth/4*2+((kScreenWidth/4-70)/2);
        _orderScrollView.contentOffset = CGPointMake(kScreenWidth*2, 0);
        self.unfinishedTab.mj_footer.state = MJRefreshStateIdle;
        
    } else if (tag == 103) {
        
        currentPage = 1;
        _waitingPaymentButton.selected = NO;
        _finishedButton.selected = NO;
        _unfinishedButton.selected = NO;
        _allButton.selected = YES;
        _lineImageView.left = kScreenWidth/4*3+((kScreenWidth/4-70)/2);
        _orderScrollView.contentOffset = CGPointMake(kScreenWidth*3, 0);
        self.allTab.mj_footer.state = MJRefreshStateIdle;
    }
    
}
#pragma mark - 初始化上拉加载控件
- (void)creatRefresh
{
    _waitingFooter =  [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(waitingPaymentRefresh)];
    [_waitingFooter setTitle:@"" forState:MJRefreshStateIdle];
    [_waitingFooter setTitle:@"正在加载..." forState:MJRefreshStateRefreshing];
    [_waitingFooter setTitle:@"若需查看更早数据，请联系客服" forState:MJRefreshStateNoMoreData];
    _waitingFooter.stateLabel.textColor = [UIColor br_textMediumGrayColor];
    self.waitingPaymentTab.mj_footer = _waitingFooter;
    
    if ([_showAndHidden isEqualToString:@"1"]) {
        self.waitingPaymentTab.mj_footer.hidden = NO;
    } else {
        self.waitingPaymentTab.mj_footer.hidden = YES;
    }
    
    _finishedFooter =  [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(finishedRefresh)];
    [_finishedFooter setTitle:@"" forState:MJRefreshStateIdle];
    [_finishedFooter setTitle:@"正在加载..." forState:MJRefreshStateRefreshing];
    [_finishedFooter setTitle:@"若需查看更早数据，请联系客服" forState:MJRefreshStateNoMoreData];
    _finishedFooter.stateLabel.textColor = [UIColor br_textMediumGrayColor];
    self.finishedTab.mj_footer = _finishedFooter;
    
    if ([_showAndHidden isEqualToString:@"1"]) {
        self.finishedTab.mj_footer.hidden = NO;
    } else {
        self.finishedTab.mj_footer.hidden = YES;
    }
    
    _unfinishedFooter =  [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(unFinishedRefresh)];
    [_unfinishedFooter setTitle:@"" forState:MJRefreshStateIdle];
    [_unfinishedFooter setTitle:@"正在加载..." forState:MJRefreshStateRefreshing];
    [_unfinishedFooter setTitle:@"若需查看更早数据，请联系客服" forState:MJRefreshStateNoMoreData];
    _unfinishedFooter.stateLabel.textColor = [UIColor br_textMediumGrayColor];
    self.unfinishedTab.mj_footer = _unfinishedFooter;
    
    if ([_showAndHidden isEqualToString:@"1"]) {
        self.unfinishedTab.mj_footer.hidden = NO;
    } else {
        self.unfinishedTab.mj_footer.hidden = YES;
    }
    
    _allFooter =  [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(allRefresh)];
    [_allFooter setTitle:@"" forState:MJRefreshStateIdle];
    [_allFooter setTitle:@"正在加载..." forState:MJRefreshStateRefreshing];
    [_allFooter setTitle:@"若需查看更早数据，请联系客服" forState:MJRefreshStateNoMoreData];
    _allFooter.stateLabel.textColor = [UIColor br_textMediumGrayColor];
    self.allTab.mj_footer = _allFooter;
    
    if ([_showAndHidden isEqualToString:@"1"]) {
        self.allTab.mj_footer.hidden = NO;
    } else {
        self.allTab.mj_footer.hidden = YES;
    }
}

- (BOOL)isWaitingPaymentTabWithTB:(UITableView *)tableView
{
    return tableView == _waitingPaymentTab;
}

- (BOOL)isfinishedTabWithTB:(UITableView *)tableView
{
    return tableView == _finishedTab;
}

- (BOOL)isunfinishedTabWithTB:(UITableView *)tableView
{
    return tableView == _unfinishedTab;
}

- (BOOL)isallTabWithTB:(UITableView *)tableView
{
    return tableView == _allTab;
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    if ([self isWaitingPaymentTabWithTB:tableView]) {
        NSLog(@"1111-------------- %ld", _waitingPaymentArray.count);
        return _waitingPaymentArray.count;
        
    } else if ([self isfinishedTabWithTB:tableView]) {
        NSLog(@"2222-------------- %ld", _finishedArray.count);
        return 1;
        
    } else if ([self isunfinishedTabWithTB:tableView]) {
        NSLog(@"333-------------- %ld", _unfinishedArray.count);
        return _unfinishedArray.count;
        
    } else {
        NSLog(@"444-------------- %ld", _allArray.count);
        return _allArray.count;
        
        
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if ([self isfinishedTabWithTB:tableView]) {
        return _finishedArray.count;
    } else {
        return 1;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    //待付款
    if ([self isWaitingPaymentTabWithTB:tableView]) {
        
        OrderCell *orderCell = [tableView dequeueReusableCellWithIdentifier:@"OrderCell"];
        
        if (orderCell == nil) {
            
            orderCell = [[OrderCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"OrderCell"];
        }
        
        if (_waitingPaymentArray.count != 0) {
            
            CompleteModel *model = [_waitingPaymentArray objectAtIndex:indexPath.section];
            
//            NSLog(@"share url ===== %@  preId = %@",model.shareUrl, model.preId);
            
            orderCell.selectionStyle = UITableViewCellSelectionStyleNone;
            orderCell.iconImageView.hidden = YES;
            
            orderCell.prescriptionType.text = model.type;
            orderCell.timeLabel.text = model.dateTime;
            orderCell.numberLabel.text = [NSString stringWithFormat:@"订单编号：%@",model.orderId];
            orderCell.serviceLabel.text = [NSString stringWithFormat:@"医技服务费：¥%@",model.serviceFee];
            orderCell.feesLabel.text = [NSString stringWithFormat:@"诊费：¥%@",model.consultationFee];
            
            if (model.smsOrder && model.buyUserMobile.length > 0) {
                orderCell.patientPhone = [NSString stringWithFormat:@"患者手机号：%@",model.buyUserMobile];
            }else{
                orderCell.patientPhone = @"";
            }
            
            NSString *pationtNameStr;
            if (model.patientName.length > 4) {
                pationtNameStr = [model.patientName substringWithRange:NSMakeRange(0, 4)];
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@...",model.patientName];
            } else {
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@",model.patientName];
            }
            
            
            if ([model.txStatus isEqualToString:@"0"]) {
                
                orderCell.remindButton.hidden = YES;
                
            } else if ([model.txStatus isEqualToString:@"2"]) {
                
                [orderCell.remindButton setTitleColor:[UIColor br_disableBgColor] forState:UIControlStateNormal];
                orderCell.remindButton.layer.borderColor = [UIColor br_disableBgColor].CGColor;
                orderCell.remindButton.selected = YES;
                orderCell.remindButton.hidden = NO;
            } else {
                [orderCell.remindButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateNormal];
                orderCell.remindButton.layer.borderColor = [UIColor br_textBlueColor].CGColor;
                orderCell.remindButton.selected = NO;
                orderCell.remindButton.hidden = NO;
            }
            
            __weak OrderCell *weakOrderCell = orderCell;
            __weak typeof(self) weakSelf = self;
            
            orderCell.remindOrEnableBlock = ^(NSInteger tag) {
                
                if (!weakOrderCell.remindButton.selected) {
                    [weakSelf remindToPayRequestPatientId:model.buyUserId orderId:model.orderId createTime:model.dateTime];
                    
                }
                
            };
            
            orderCell.showSendButton = model.shareUrl.length > 0 ? YES : NO;
            
            orderCell.clickSendToPatient = ^(){
                NSLog(@"发送给患者=====");
                if (model.smsOrder) {
                    //短信开方
                    [weakSelf sendSmsOrderWithOrderId:model.orderId mobile:model.buyUserMobile];
                }else{
                    //微信开方
                    [weakSelf shareContentWithShareUrl:model.shareUrl preId:model.orderId takerName:model.patientName];
                }
            };
        }
        
        return orderCell;
        
    } 
    //已完成
    else if ([self isfinishedTabWithTB:tableView]) {
        
        CompleteModel *model;
        if (_finishedArray.count != 0) {
            
            model = [_finishedArray objectAtIndex:indexPath.row];
            
        }
        
        // height 180
        if ([model.dataType isEqualToString:@"1"]) {
            
            CompleteTotalOrGapCell *totalOrGapCell = [tableView dequeueReusableCellWithIdentifier:@"CompleteTotalOrGapCell"];
            
            if (totalOrGapCell == nil) {
                
                totalOrGapCell = [[CompleteTotalOrGapCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"CompleteTotalOrGapCell"];
            }
            totalOrGapCell.selectionStyle = UITableViewCellSelectionStyleNone;
            totalOrGapCell.separatorInset = UIEdgeInsetsZero;
            totalOrGapCell.dateLabel.text = model.month2;
            totalOrGapCell.dateLabel.textColor = [UIColor colorWithHex:0x212121];
            //当月订单量
            totalOrGapCell.basisLabel.text = model.orderCount;
            totalOrGapCell.basisLabel.textColor = [UIColor br_textBlueColor];
            //标准药费
            totalOrGapCell.standardLabel.text = model.standardDrugPrice;
            totalOrGapCell.standardLabel.textColor = [UIColor br_textBlueColor];
            //诊费
            totalOrGapCell.diagnosticFeeLabel.text = model.consultationFee;
            //医技服务费
            totalOrGapCell.serviceFeeLabel.text = model.medicalServiceFee;
            float percent = [model.orderBalance floatValue];
            if ([model.orderBalance integerValue] > 0) {
                totalOrGapCell.promptLabel.text = [NSString stringWithFormat:@"（距离平台额外奖励还差¥%@）",model.orderBalance];
            } else {
                totalOrGapCell.promptLabel.text = @"";
            }
            totalOrGapCell.lineImageView.image = [UIImage imageNamed:@"order_line"];
            
            return totalOrGapCell;
            
        }  
        //height 180
        else if ([model.dataType isEqualToString:@"2"]) {
            
            CompleteTotalOrGapCell *totalOrGapCell = [tableView dequeueReusableCellWithIdentifier:@"CompleteTotalOrGapCell"];
            
            if (totalOrGapCell == nil) {
                
                totalOrGapCell = [[CompleteTotalOrGapCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"CompleteTotalOrGapCell"];
            }
            totalOrGapCell.selectionStyle = UITableViewCellSelectionStyleNone;
            totalOrGapCell.separatorInset = UIEdgeInsetsZero;
            totalOrGapCell.dateLabel.text = model.month2;
            totalOrGapCell.dateLabel.textColor = [UIColor br_textMediumGrayColor];
            totalOrGapCell.basisLabel.text = model.orderCount;
            totalOrGapCell.basisLabel.textColor = [UIColor br_textDarkBlueColor];
            totalOrGapCell.standardLabel.text = model.standardDrugPrice;
            totalOrGapCell.standardLabel.textColor = [UIColor br_textDarkBlueColor];
            totalOrGapCell.diagnosticFeeLabel.textColor = [UIColor br_textDarkBlueColor];
            totalOrGapCell.serviceFeeLabel.textColor = [UIColor br_textDarkBlueColor];
            totalOrGapCell.promptLabel.text = @"";
            totalOrGapCell.lineImageView.image = [UIImage imageNamed:@"order_line_hui"];
            //诊费
            totalOrGapCell.diagnosticFeeLabel.text = model.consultationFee;
            //医技服务费
            totalOrGapCell.serviceFeeLabel.text = model.medicalServiceFee;
            
            return totalOrGapCell;
            
        } 
        // 0 和 其他  ｜
        else {
            
//            CompleteDetailCell *detailCell = [tableView dequeueReusableCellWithIdentifier:@"CompleteDetailCell"];
            CompleteDetailCell *detailCell = [tableView dequeueReusableCellWithIdentifier:@"CompleteDetailCell" forIndexPath:indexPath];
            
//            if (detailCell == nil) {
//                
//                detailCell = [[CompleteDetailCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"CompleteDetailCell"];
//            }
            
            detailCell.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
            detailCell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            detailCell.prescriptionType.text = model.type;
            detailCell.timeLabel.text = model.dateTime;
            detailCell.numberLabel.text = [NSString stringWithFormat:@"订单编号：%@",model.orderId];
            detailCell.serviceLabel.text = [NSString stringWithFormat:@"医技服务费：¥%@",model.serviceFee];
            detailCell.feesLabel.text = [NSString stringWithFormat:@"诊费：¥%@",model.consultationFee];
            
            detailCell.patientPhone = [NSString stringWithFormat:@""];
            
            NSString *pationtNameStr;
            if (model.patientName.length > 4) {
                pationtNameStr = [model.patientName substringWithRange:NSMakeRange(0, 4)];
                detailCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@...",model.patientName];
            } else {
                detailCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@",model.patientName];
            }
            
            
            if (model.smsOrder && model.buyUserMobile.length > 0) {
                detailCell.patientPhone = [NSString stringWithFormat:@"患者手机号：%@",model.buyUserMobile];
            }else{
                detailCell.patientPhone = @"";
            }
            
            // 设置查询物流按钮的点击事件
            __weak typeof(self) weakSelf = self;
            detailCell.trackButtonClickBlock = ^{
                // 这里先留空，后续实现查询物流的功能
                NSLog(@"查询物流按钮点击 - 订单ID: %@", model.orderId);
                
                // 使用物流单号构建查询URL
                NSString *logisticsUrl = [NSString stringWithFormat:@"%@wxSubmit/delivery?flag=0&preId=%@", kDomain, model.orderId];
                
                // 跳转到物流查询页面
                WzdWebViewController *wzdWebViewController = [[WzdWebViewController alloc] init];
                wzdWebViewController.likeUrl = logisticsUrl;
                wzdWebViewController.showTitle = @"物流状态";
                [weakSelf.navigationController pushViewController:wzdWebViewController animated:YES];
            };
            
            return detailCell;
        }
        
    } else if ([self isunfinishedTabWithTB:tableView]) {
        //已过期
        
        OrderCell *orderCell = [tableView dequeueReusableCellWithIdentifier:@"OrderCell"];
        
        if (orderCell == nil) {
            
            orderCell = [[OrderCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"OrderCell"];
        }
        if (_unfinishedArray.count != 0) {
            
            //            orderCell.selectionStyle = UITableViewCellSelectionStyleNone;
            //
            //            [orderCell.remindButton setTitle:@"重新启用" forState:UIControlStateNormal];
            //            orderCell.iconImageView.hidden = YES;
            
            // 检查是否有数据以及用户是否已手动关闭过提示
            if (_unfinishedArray.count > 0 && ![[[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultisOrder] isEqualToString:@"1"]) {
                
                // 使用动态计算的高度而不是固定的30pt
                CGFloat noticeHeight = [_orderNoticeView getNoticeViewHeight];
                [_orderNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.height.mas_equalTo(noticeHeight);
                }];
                
                _orderNoticeView.hidden = NO;
                // 移除5秒自动消失逻辑，只有手动关闭才会记录状态
            }
            
            CompleteModel *model = [_unfinishedArray objectAtIndex:indexPath.section];
            
            orderCell.selectionStyle = UITableViewCellSelectionStyleNone;
            orderCell.remindButton.hidden = YES;
            
            orderCell.prescriptionType.text = model.type;
            orderCell.timeLabel.text = model.dateTime;
            orderCell.numberLabel.text = [NSString stringWithFormat:@"订单编号：%@",model.orderId];
            orderCell.serviceLabel.text = [NSString stringWithFormat:@"医技服务费：¥%@",model.serviceFee];
            orderCell.feesLabel.text = [NSString stringWithFormat:@"诊费：¥%@",model.consultationFee];
            
            NSString *pationtNameStr;
            if (model.patientName.length > 4) {
                pationtNameStr = [model.patientName substringWithRange:NSMakeRange(0, 4)];
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@...",model.patientName];
            } else {
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@",model.patientName];
            }
            
            if (model.smsOrder && model.buyUserMobile.length > 0) {
                orderCell.patientPhone = [NSString stringWithFormat:@"患者手机号：%@",model.buyUserMobile];
            }else{
                orderCell.patientPhone = @"";
            }
            
            //            orderCell.remindOrEnableBlock = ^(NSInteger tag) {
            //
            //                [self toEnableTheRequestOrderId:model.orderId indexTag:indexPath.section];
            //            };
            
            // 移除自动设置用户偏好的逻辑，只有手动关闭提示时才设置
            
        }
        
        return orderCell;
        
    } else {
        
        OrderCell *orderCell = [tableView dequeueReusableCellWithIdentifier:@"OrderCell"];
        
        if (orderCell == nil) {
            
            orderCell = [[OrderCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"OrderCell"];
        }
        
        if (_allArray.count != 0) {
            
            CompleteModel *model = [_allArray objectAtIndex:indexPath.section];
            
            orderCell.selectionStyle = UITableViewCellSelectionStyleNone;
            orderCell.remindButton.hidden = YES;
            orderCell.iconImageView.hidden = NO;
            
            orderCell.prescriptionType.text = model.type;
            orderCell.timeLabel.text = model.dateTime;
            orderCell.numberLabel.text = [NSString stringWithFormat:@"订单编号：%@",model.orderId];
            orderCell.serviceLabel.text = [NSString stringWithFormat:@"医技服务费：¥%@",model.serviceFee];
            orderCell.feesLabel.text = [NSString stringWithFormat:@"诊费：¥%@",model.consultationFee];
            
            NSString *pationtNameStr;
            if (model.patientName.length > 4) {
                pationtNameStr = [model.patientName substringWithRange:NSMakeRange(0, 4)];
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@...",model.patientName];
            } else {
                orderCell.nameLabel.text = [NSString stringWithFormat:@"患者：%@",model.patientName];
            }
            
            if (model.smsOrder && model.buyUserMobile.length > 0) {
                orderCell.patientPhone = [NSString stringWithFormat:@"患者手机号：%@",model.buyUserMobile];
            }else{
                orderCell.patientPhone = @"";
            }
            
            if ([model.state isEqualToString:@"2"]) {
                
                orderCell.iconImageView.image = [UIImage imageNamed:@"order_daifukuan"];
                
            } else if ([model.state isEqualToString:@"1"]) {
                
                orderCell.iconImageView.image = [UIImage imageNamed:@"order_yiwancheng"];
                
            } else {
                orderCell.iconImageView.image = [UIImage imageNamed:@"order_yiguoqi"];
            }
        }
        
        return orderCell;
        
    }
}
#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    
    if ([self isfinishedTabWithTB:tableView]) {
        
        CompleteModel *model;
        
        if (_finishedArray.count != 0) {
            model = [_finishedArray objectAtIndex:indexPath.row];
        }
        
        
        if ([model.dataType isEqualToString:@"0"]) {
//            CompleteDetailCell
//            return 115;
            return UITableViewAutomaticDimension;
        } else if ([model.dataType isEqualToString:@"1"] || [model.dataType isEqualToString:@"2"]) {
            // CompleteTotalOrGapCell
            return 180;
        } else {
            //CompleteDetailCell
//            return 105;
            return UITableViewAutomaticDimension;
        }
        
    } else {
//        return 125;
        return UITableViewAutomaticDimension;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    if ([self isfinishedTabWithTB:tableView]) {
        return 0;
    } else {
        return 10;
    }
}
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor br_backgroundColor];
    return view;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    CompleteModel *model = [[CompleteModel alloc] init];
    
    if ([self isWaitingPaymentTabWithTB:tableView]) {
        
        model = [_waitingPaymentArray objectAtIndex:indexPath.section];
        
    } else if ([self isfinishedTabWithTB:tableView]) {
        
        model = [_finishedArray objectAtIndex:indexPath.row];
        
    } else if ([self isunfinishedTabWithTB:tableView]) {
        
        model = [_unfinishedArray objectAtIndex:indexPath.section];
        
    } else if ([self isallTabWithTB:tableView]) {
        
        model = [_allArray objectAtIndex:indexPath.section];
    }
    
    if ([model.dataType isEqualToString:@"1"] || [model.dataType isEqualToString:@"2"]) {
        
    } else {
        
        MedicatedInfoViewController *medicatedInfo = [[MedicatedInfoViewController alloc] init];
        if (_waitingPaymentButton.selected ) {
            
            medicatedInfo.btnShowOrNot = NSBtnShow;
            [medicatedInfo returnText:^(NSInteger tag) {
                
                if (tag == 1) {
                    [_waitingPaymentArray removeAllObjects];
                    [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
                }
            }];
            
        }
        medicatedInfo.orderId = model.orderId;
        [self.navigationController pushViewController:medicatedInfo animated:YES];
        
    }
    
}

#pragma mark - 分享
- (void)shareContentWithShareUrl:(NSString *)shareUrl preId:(NSString *)preId takerName:(NSString *)takerName{
    
    NSString *docName = [[UserManager shareInstance] getUserInfoByKey:kUserDefaultName];
    NSString *titleString = [NSString stringWithFormat:@"%@大夫已为你开好药方!",docName];
    
//    NSString *takerName = [self.orderDict objectForKey:@"takerName"];
    NSString *content = [NSString stringWithFormat:@"%@大夫给%@的药方，订单号：%@",docName,takerName,preId];
    
    UIImage *thumbImage = [UIImage imageNamed:@"logo_icon"];
    
    WXWebpageObject *webpageObject = [WXWebpageObject object];
    webpageObject.webpageUrl = shareUrl;
    WXMediaMessage *message = [WXMediaMessage message];
    message.title = titleString;
//            message.description = [NSString stringWithFormat:@"邀请码：%@\n必然中医下载地址：%@",self.qrCodeField.text,kDownloadAPPUrl];
    message.description = content;
    [message setThumbImage:thumbImage];
    message.mediaObject = webpageObject;
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.bText = NO;
    req.message = message;
    req.scene = WXSceneSession;
    
    __weak __typeof(self)weakSelf = self;
    [WXApi sendReq:req completion:^(BOOL success) {
        if (success) {
            NSLog(@"分享成功===");
            
            [weakSelf.navigationController popToRootViewControllerAnimated:YES];
        }
        else{
            NSLog(@"分享失败===");
            
            [weakSelf.view makeToast:@"分享失败" duration:2 position:CSToastPositionCenter];
        }
    }];
    
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    if (scrollView == _orderScrollView) {
        
        NSLog(@"%f",scrollView.contentOffset.x);
        CGFloat f = scrollView.contentOffset.x;
        if (f == 0) {
            [self hearderViewSegmentdedUI:102];
            if ([_showAndHidden isEqualToString:@"1"]) {
                
                [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
            } else {
                [_waitingPaymentTab reloadData];
            }
            
        } else if (f == kScreenWidth) {
            [self hearderViewSegmentdedUI:101];
            if ([_showAndHidden isEqualToString:@"1"]) {
                
                [self medicationOrdersRequestCurrentPage:currentPage payState:1 lastYearMonth:@"" pageSize:@"20"];
            }
            else {
                [_finishedTab reloadData];
            }
        } else if (f == kScreenWidth*2) {
            [self hearderViewSegmentdedUI:107];
            if ([_showAndHidden isEqualToString:@"1"]) {
                
                [self medicationOrdersRequestCurrentPage:currentPage payState:7 lastYearMonth:@"" pageSize:@"20"];
            }
            else {
                [_unfinishedTab reloadData];
            }
        } else if (f == kScreenWidth*3) {
            [self hearderViewSegmentdedUI:103];
            if ([_showAndHidden isEqualToString:@"1"]) {
                
                [self medicationOrdersRequestCurrentPage:currentPage payState:-1 lastYearMonth:@"" pageSize:@"20"];
            }
            else {
                [_allTab reloadData];
            }
        }
    } else {
        
        CGFloat sectionHeaderHeight = 10;//设置你footer高度
        if (scrollView.contentOffset.y<=sectionHeaderHeight&&scrollView.contentOffset.y>=0) {
            scrollView.contentInset = UIEdgeInsetsMake(-scrollView.contentOffset.y, 0, 0, 0);
        } else if (scrollView.contentOffset.y>=sectionHeaderHeight) {
            scrollView.contentInset = UIEdgeInsetsMake(-sectionHeaderHeight, 0, 0, 0);
        }
        
    }
}

#pragma mark - 获取用药订单列表数据
- (void)medicationOrdersRequestCurrentPage:(NSInteger )currentPage payState:(NSInteger )payState lastYearMonth:(NSString *)time pageSize:(NSString *)pageSize
{
    if (payState == 1 && _finishedArray.count > 0) {
        
        CompleteModel *model = [_finishedArray objectAtIndex:_finishedArray.count-1];
        lastYearMouth = model.dateTime;
        
        NSLog(@"lastYearMouth == %@",lastYearMouth);
    }
    __weak typeof(self) mySelf = self;
    NSDictionary *dict = @{@"method_code":@"000250",
                           @"page":[NSString stringWithFormat:@"%ld",currentPage],
                           @"payState":[NSString stringWithFormat:@"%ld",payState],
                           @"pageSize":pageSize,
                           @"lastYearMonth":lastYearMouth
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    hud.minShowTime = 0;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        [hud hideAnimated:YES];
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            [self removeNodataView];
            if (_waitingPaymentButton.selected) {
                
                if (currentPage == 1) {
                    
                    [_waitingPaymentArray setArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_waitingPaymentTab reloadData];
                    if (_waitingPaymentArray.count > 0) {
                        
                        NSIndexPath* indexPat = [NSIndexPath indexPathForRow:0 inSection:0];
                        [_waitingPaymentTab scrollToRowAtIndexPath:indexPat atScrollPosition:UITableViewScrollPositionBottom animated:NO];
                    }
                    
                } else {
                    
                    [_waitingPaymentArray addObjectsFromArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_waitingPaymentTab reloadData];
                }
                
                
                if (isRemind) {
                    [self.waitingPaymentTab setContentOffset:CGPointMake(0,0) animated:NO];
                    isRemind = NO;
                }

                
            } else if (_finishedButton.selected) {
                
                if (currentPage == 1) {
                    
                    [_finishedArray setArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_finishedTab reloadData];
                    
                    if (_finishedArray.count > 0) {
                        
                        NSIndexPath* indexPat = [NSIndexPath indexPathForRow:0 inSection:0];
                        [_finishedTab scrollToRowAtIndexPath:indexPat atScrollPosition:UITableViewScrollPositionBottom animated:NO];
                    }
                    
                    
                } else {
                    
                    [_finishedArray addObjectsFromArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    
                    [_finishedTab reloadData];

                }
                
                
            } else if (_unfinishedButton.selected) {
                
                if (currentPage == 1) {
                    
                    [_unfinishedArray setArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_unfinishedTab reloadData];
                    
                    if (_unfinishedArray.count > 0) {
                        
                        NSIndexPath* indexPat = [NSIndexPath indexPathForRow:0 inSection:0];
                        [_unfinishedTab scrollToRowAtIndexPath:indexPat atScrollPosition:UITableViewScrollPositionBottom animated:NO];
                    }
                    
                } else {
                    
                    [_unfinishedArray addObjectsFromArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_unfinishedTab reloadData];

                }
                
            } else {
                
                if (currentPage == 1) {
                    
                    [_allArray setArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_allTab reloadData];
                    if (_allArray.count > 0) {
                        
                        NSIndexPath* indexPat = [NSIndexPath indexPathForRow:0 inSection:0];
                        [_allTab scrollToRowAtIndexPath:indexPat atScrollPosition:UITableViewScrollPositionBottom animated:NO];
                    }
                    
                } else {
                    
                    [_allArray addObjectsFromArray:[CompleteModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"list"]]];
                    [_allTab reloadData];
                }
            }
        }
        
        totalPageSize = [[responseObject objectForKey:@"totalPageSize"] integerValue];
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            [self createNoWIFIView];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
    }];
}

#pragma mark - 提醒支付接口
- (void)remindToPayRequestPatientId:(NSString *)patientId orderId:(NSString *)orderId createTime:(NSString *)createTime
{
    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    NSDictionary *dict = @{@"method_code"   :@"000247",
                           @"doctorId"      :[UserManager shareInstance].getUserId,
                           @"patientId"     :patientId,
                           @"orderId"       :orderId,
                           @"createTime"    :createTime
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    hud.minShowTime = 0;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        [hud hideAnimated:YES];
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            isRemind = YES;
            [self.view makeToast:@"提醒成功" duration:2 position:CSToastPositionCenter];
            [_waitingPaymentArray removeAllObjects];
            currentPage = 1;
            [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
            
        }else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
    }];
}

#pragma mark - 短信发送接口
- (void)sendSmsOrderWithOrderId:(NSString *)orderId mobile:(NSString *)mobile {
    
    __weak typeof(self) weakSelf = self;
    
   __block BRAlertView *alertView = [Utils br_showAlertHasMobileInputMessage:@"请输入患者手机号" mobileText:mobile okButtonTitle:@"发送" placeholder:@"输入手机号" completion:^(BOOL isOk, NSString *text) {
        if (isOk) {
            NSLog(@"输入的手机号 == %@",text);
            
            if ([text length] == 0) {
                [alertView makeToast:@"请输入患者手机号码" duration:2 position:CSToastPositionCenter];
            }else if ([text length] < 11) {
                [alertView makeToast:@"请输入11位手机号" duration:2 position:CSToastPositionCenter];
            }else {
                [alertView close];
                
                
                NSDictionary *dict = @{
                    @"method_code" : @"000446",
                    @"apiVer" : @"4",
                    @"orderId" : orderId,
                    @"mobile" : text,
                    @"sys_type" : @"1"
                };
                
                __block MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
                __weak typeof(self) weakSelf = self;
                
                [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
                    
                } success:^(NSURLSessionDataTask *task, id responseObject) {
                    [hud removeFromSuperview];
                    [hud hideAnimated:NO];
                    
                    NSString *code = [responseObject objectForKey:@"code"];
                    
                    if ([code isEqualToString:@"0000"]) {
                        
                        [[[UIApplication sharedApplication] delegate].window makeToast:@"发送成功" duration:kToastDuration position:CSToastPositionCenter];
                        
                        //刷新当前界面
                        [weakSelf refreshCurrentPage];
                        
                    }else {
                        NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                        [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                    }
                    
                } failure:^(NSURLSessionDataTask *task, NSError *error) {
                    [hud removeFromSuperview];
                    [hud hideAnimated:NO];
                    [self.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
                }];
            }
        }
    }];
    
}

#pragma mark - 重新启用接口
- (void)toEnableTheRequestOrderId:(NSString *)orderId indexTag:(NSInteger)indexTag
{
    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    NSDictionary *dict = @{@"method_code"   :@"000248",
                           @"orderId"       :orderId
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            [_unfinishedArray removeObjectAtIndex:indexTag];
            
            [_unfinishedTab reloadData];
        }
        [hud hideAnimated:YES];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
    }];
}
#pragma mark - 更新显示或隐藏状态
- (void)updateTheDisplayStateValue:(NSString *)value smsCode:(NSString *)smsCode
{
    
    NSDictionary *dict = @{@"method_code"   :@"000206",
                           @"type"          :@"1",
                           @"value"         :value,
                           @"smsCode"       :smsCode
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    hud.minShowTime = 0;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        [hud hideAnimated:YES];
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            if ([value isEqualToString:@"1"]) {
                
                [_button setImage:[UIImage imageNamed:@"order_yincangdingdan"] forState:UIControlStateNormal];
                [_button setTitle:@"隐藏" forState:UIControlStateNormal];
                _button.selected = NO;
                _orderScrollView.scrollEnabled = YES;
                _showAndHidden = @"1";
                _topButtonView.hidden = NO;
                time = 1;
                self.waitingPaymentTab.mj_footer.hidden = NO;
                self.finishedTab.mj_footer.hidden = NO;
                self.unfinishedTab.mj_footer.hidden = NO;
                self.allTab.mj_footer.hidden = NO;
                
                currentPage = 1;
                if (_waitingPaymentButton.selected) {
                    self.waitingPaymentTab.mj_footer.state = MJRefreshStateIdle;
                    [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
                    
                } else if (_finishedButton.selected) {
                    self.finishedTab.mj_footer.state = MJRefreshStateIdle;
                    [self medicationOrdersRequestCurrentPage:currentPage payState:1 lastYearMonth:@"" pageSize:@"20"];
                    
                }else if (_unfinishedButton.selected) {
                    self.unfinishedTab.mj_footer.state = MJRefreshStateIdle;
                    [self medicationOrdersRequestCurrentPage:currentPage payState:7 lastYearMonth:@"" pageSize:@"20"];
                    
                }else if (_allButton.selected) {
                    self.allTab.mj_footer.state = MJRefreshStateIdle;
                    [self medicationOrdersRequestCurrentPage:currentPage payState:-1 lastYearMonth:@"" pageSize:@"20"];
                    
                }
                backStr = @"0";
                [_backview removeFromSuperview];
                [_orderTextField resignFirstResponder];
            } else {
                
                
            }
        } else {
            [_orderView makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
    }];
}
#pragma mark - 上拉加载
//刷新当前界面
- (void)refreshCurrentPage {
    
    if (buttonTag == 102) {
        [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
    }else if (buttonTag == 101){
        [self medicationOrdersRequestCurrentPage:currentPage payState:1 lastYearMonth:@"" pageSize:@"20"];
    }else if (buttonTag == 107){
        [self medicationOrdersRequestCurrentPage:currentPage payState:7 lastYearMonth:@"" pageSize:@"20"];
    }else if (buttonTag == 103){
        [self medicationOrdersRequestCurrentPage:currentPage payState:-1 lastYearMonth:@"" pageSize:@"20"];
    }
}

- (void)waitingPaymentRefresh
{
    if (currentPage >= totalPageSize) {
        
        self.waitingPaymentTab.mj_footer.state = MJRefreshStateNoMoreData;
        
    } else if (currentPage < totalPageSize) {
        
        currentPage++;
        [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
        
        self.waitingPaymentTab.mj_footer.state = MJRefreshStateIdle;
    }
}
- (void)finishedRefresh
{
    if (currentPage >= totalPageSize) {
        
        self.finishedTab.mj_footer.state = MJRefreshStateNoMoreData;
        
    } else if (currentPage < totalPageSize) {
        
        currentPage++;
        [self medicationOrdersRequestCurrentPage:currentPage payState:1 lastYearMonth:@"" pageSize:@"20"];
        
        self.finishedTab.mj_footer.state = MJRefreshStateIdle;
    }
}
- (void)unFinishedRefresh
{
    if (currentPage >= totalPageSize) {
        
        self.unfinishedTab.mj_footer.state = MJRefreshStateNoMoreData;
        
    } else if (currentPage < totalPageSize) {
        
        currentPage++;
        [self medicationOrdersRequestCurrentPage:currentPage payState:7 lastYearMonth:@"" pageSize:@"20"];
        
        self.unfinishedTab.mj_footer.state = MJRefreshStateIdle;
    }
}
- (void)allRefresh
{
    if (currentPage >= totalPageSize) {
        
        self.allTab.mj_footer.state = MJRefreshStateNoMoreData;
        
    } else if (currentPage < totalPageSize) {
        
        currentPage++;
        [self medicationOrdersRequestCurrentPage:currentPage payState:-1 lastYearMonth:@"" pageSize:@"20"];
        
        self.allTab.mj_footer.state = MJRefreshStateIdle;
    }
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    [textField resignFirstResponder];
    return YES;
}
#pragma mark - 无网络界面
- (void)createNoWIFIView {
    
    self.noDataView = [[BRNoDataView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height) withImage:[UIImage imageNamed:@"no_WIFI"] withText:@"网络异常，请手动点击重新加载"];
    
    [self.view addSubview:self.noDataView];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapClick)];
    
    [self.noDataView addGestureRecognizer:tap];
}
- (void)tapClick
{
    if (_waitingPaymentButton.selected) {
        
        [self medicationOrdersRequestCurrentPage:currentPage payState:2 lastYearMonth:@"" pageSize:@"20"];
        
    } else if (_finishedButton.selected) {
        
        [self medicationOrdersRequestCurrentPage:currentPage payState:1 lastYearMonth:@"" pageSize:@"20"];
        
    }else if (_unfinishedButton.selected) {
        
        [self medicationOrdersRequestCurrentPage:currentPage payState:7 lastYearMonth:@"" pageSize:@"20"];
        
    }else if (_allButton.selected) {
        
        [self medicationOrdersRequestCurrentPage:currentPage payState:-1 lastYearMonth:@"" pageSize:@"20"];
        
    }
}
- (void)removeNodataView {
    [self.noDataView removeFromSuperview];
    self.noDataView = nil;
}
#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    if ([_showAndHidden isEqualToString:@"2"]) {
        return [UIImage imageNamed:@""];
    } else {
        return [UIImage imageNamed:@"zanwudingdan"];
    }
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    if ([_showAndHidden isEqualToString:@"2"]) {
        
        NSDictionary *attributes = @{NSFontAttributeName : kFontLight(15),
                                     NSForegroundColorAttributeName : [UIColor lightGrayColor]
                                     };
        NSAttributedString *str = [[NSAttributedString alloc] initWithString:@"显示用药订单，点击页面右上角的隐藏标识" attributes:attributes];
        
        NSMutableAttributedString *string = [[NSMutableAttributedString alloc] initWithAttributedString:str];
        
        
        UIImage *image = [UIImage imageNamed:@"order_xianshidingdan"];
        
        NSTextAttachment *textAttachment = [[NSTextAttachment alloc] initWithData:nil ofType:nil] ;
        textAttachment.image = image;
        textAttachment.bounds = CGRectMake(0, -10, image.size.width, image.size.height);
        
        NSAttributedString *textAttachmentString = [NSAttributedString attributedStringWithAttachment:textAttachment];
        
        [string replaceCharactersInRange:NSMakeRange(15, 2) withAttributedString:textAttachmentString];
        
        return string;
    } else {
        NSString *text = @"暂无订单记录";
        
        NSDictionary *attributes = @{NSFontAttributeName : [UIFont systemFontOfSize:14],
                                     NSForegroundColorAttributeName : [UIColor lightGrayColor]
                                     };
        
        return [[NSAttributedString alloc] initWithString:text attributes:attributes];
    }
}

- (CGFloat)verticalOffsetForEmptyDataSet:(UIScrollView *)scrollView {
    return -50;
}
#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView {
    return YES;
}
#pragma mark - 隐藏提示条
- (void)hiddenNoticeView {
    
    // 用户手动关闭时，保存状态到用户偏好设置
    [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:kUserDefaultisOrder];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    _orderNoticeView.hidden = YES;
    
    [_orderNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(0);
    }];
    [_unfinishedTab mas_updateConstraints:^(MASConstraintMaker *make) {
        
        make.height.mas_equalTo(_orderScrollView.frame.size.height);
        
    }];
}
@end
