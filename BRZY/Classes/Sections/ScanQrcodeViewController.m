//
//  ScanQrcodeViewController.m
//  BRZY
//
//  Created by YYKKJ on 2018/1/2.
//  Copyright © 2018年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "ScanQrcodeViewController.h"

#import <AVFoundation/AVFoundation.h>
#import <AVFoundation/AVFoundation.h>
#import "UIView+BR.h"
#import "BRAlertView.h"
#import <AssetsLibrary/AssetsLibrary.h>
#import "UIImage+BR.h"
#import <UIKit/UIKit.h>

static const CGFloat kMargin = 30;
#define kBorderW self.view.frame.size.height/8*2

@interface ScanQrcodeViewController ()<AVCaptureMetadataOutputObjectsDelegate, UIAlertViewDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate>
{
    AVAuthorizationStatus authStatus;
    NSString *docterID;
    
    BOOL isReading;
    
    // For preventing duplicate invalid QR code alerts
    NSString *lastInvalidQRCode;
    NSTimeInterval lastInvalidQRCodeTime;
}

@property (strong, nonatomic)  UIView *viewPreview;
@property (strong, nonatomic)  UIView *scanWindowView;
@property (strong, nonatomic)  UIView *scanNetImageView;

//捕捉会话
@property (nonatomic, strong) AVCaptureSession *captureSession;
//展示layer
@property (nonatomic, strong) AVCaptureVideoPreviewLayer *videoPreviewLayer;

@end

@implementation ScanQrcodeViewController

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.navigationController setNavigationBarHidden:YES animated:YES];
    [self resumeAnimation];
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self.navigationController setNavigationBarHidden:NO animated:YES];
    [self turnTorchOn:NO];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [_captureSession stopRunning];
    });
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // Initialize debounce variables
    lastInvalidQRCode = nil;
    lastInvalidQRCodeTime = 0;
    
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"确定" forState:UIControlStateNormal];
        [alertView showAlertView:@"请在iPhone的\"设置-隐私-相机\"选项中，允许必然中医访问你的相机" completion:^{
            [alertView close];
        }];
        
    }
    
    //设置扫描整体页面
    [self setMaskView];
    
    //创建遮罩
    [self setShadeView];
    
    //创建返回按钮
    [self creatNavView];
    
    //创建扫描框
    [self setScanWindowView];

    // 在扫描框上方添加网页扫码提示（仅医案导出入口才显示）
    if (self.shouldShowWebExportHint) {
        [self setTopWebHintLabel];
    }

    //创建底部部分
    [self setBottomBarView];
    
    [self startReading];
    
}

// 顶部提示：电脑端打开网址并扫码（网址单独一行，更醒目）
- (void)setTopWebHintLabel {
    CGFloat tip1Height = 18.0;
    CGFloat urlHeight = 22.0;
    CGFloat spacing = 4.0;
    CGFloat containerHeight = tip1Height + spacing + urlHeight;

    CGFloat topY = CGRectGetMinY(_scanWindowView.frame) - containerHeight - 25.0; // 距离扫描框底部间隔25
    CGFloat navSafeTop = (isiPhoneX ? 50.0 : 25.0) + 36.0; // 避免与返回/闪光灯/相册重叠
    if (topY < navSafeTop) {
        topY = navSafeTop;
    }

    UIView *container = [[UIView alloc] initWithFrame:CGRectMake(0, topY, self.view.bounds.size.width, containerHeight)];
    container.backgroundColor = [UIColor clearColor];
    [self.view addSubview:container];

    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, container.bounds.size.width, tip1Height)];
    tipLabel.text = @"请在电脑浏览器打开";
    tipLabel.textColor = [UIColor whiteColor];
    tipLabel.textAlignment = NSTextAlignmentCenter;
    tipLabel.font = [UIFont systemFontOfSize:14];
    tipLabel.backgroundColor = [UIColor clearColor];
    [container addSubview:tipLabel];

    // 创建URL行容器，用于居中显示URL和复制按钮
    UIView *urlRowContainer = [[UIView alloc] initWithFrame:CGRectMake(0, tip1Height + spacing, container.bounds.size.width, urlHeight)];
    urlRowContainer.backgroundColor = [UIColor clearColor];
    [container addSubview:urlRowContainer];
    
    // URL文字
    NSString *urlText = @"https://yian.haoniuzhongyi.top";
    UILabel *urlLabel = [[UILabel alloc] init];
    urlLabel.text = urlText;
    urlLabel.textColor = [UIColor whiteColor];
    urlLabel.font = [UIFont boldSystemFontOfSize:17];
    urlLabel.backgroundColor = [UIColor clearColor];
    [urlLabel sizeToFit];
    
    // 复制按钮
    UIButton *copyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [copyButton setTitle:@"复制" forState:UIControlStateNormal];
    [copyButton setTitleColor:[UIColor colorWithRed:0.0 green:0.5 blue:1.0 alpha:1.0] forState:UIControlStateNormal]; // 蓝色
    copyButton.titleLabel.font = [UIFont systemFontOfSize:14];
    [copyButton sizeToFit];
    
    // 计算总宽度并居中布局
    CGFloat spacing2 = 10.0; // URL和复制按钮之间的间距
    CGFloat totalWidth = urlLabel.bounds.size.width + spacing2 + copyButton.bounds.size.width;
    CGFloat startX = (urlRowContainer.bounds.size.width - totalWidth) / 2.0;
    
    // 设置frame
    urlLabel.frame = CGRectMake(startX, 0, urlLabel.bounds.size.width, urlHeight);
    copyButton.frame = CGRectMake(startX + urlLabel.bounds.size.width + spacing2, 
                                  (urlHeight - copyButton.bounds.size.height) / 2.0, 
                                  copyButton.bounds.size.width, 
                                  copyButton.bounds.size.height);
    
    [urlRowContainer addSubview:urlLabel];
    [urlRowContainer addSubview:copyButton];
    
    // 添加复制功能
    [[copyButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
         UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
         pasteboard.string = urlText;
         [self.view makeToast:@"已复制到粘贴板" duration:2 position:CSToastPositionCenter];
     }];
}


- (void)creatNavView {
    
    UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [backButton setImage:[UIImage imageNamed:@"saoyisao_fanhui"] forState:UIControlStateNormal];
    [self.view addSubview:backButton];
    
    [[backButton rac_signalForControlEvents:UIControlEventTouchUpInside]
     subscribeNext:^(id x) {
         [self.navigationController popViewControllerAnimated:YES];
     }];
    
    
    [backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        
        make.left.equalTo(self.view).offset(15);
        make.top.equalTo(self.view).offset(isiPhoneX ? 50:25);
        make.width.and.height.mas_equalTo(32);
    }];
    
    UIButton * flashBtn=[UIButton buttonWithType:UIButtonTypeCustom];
    flashBtn.frame = CGRectMake(self.view.br_width-47,(isiPhoneX ? 50:25), 32, 32);
    [flashBtn setImage:[UIImage imageNamed:@"saoyisao_scan_btn_flash_up"] forState:UIControlStateNormal];
    [flashBtn setImage:[UIImage imageNamed:@"saoyisao_scan_btn_flash_down"] forState:UIControlStateSelected];
    flashBtn.contentMode=UIViewContentModeScaleAspectFit;
    [flashBtn addTarget:self action:@selector(openFlash:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:flashBtn];
    
    
    //相册按钮 - 移动到左下方
    UIImage *libraryImage = [UIImage imageNamed:@"chat_inputpanel_photo"];
    
    UIButton *libraryButton = [UIButton buttonWithType:UIButtonTypeCustom];
    // 计算左下方位置：距离左边15，距离底部安全区域上方60
    CGFloat bottomMargin = isiPhoneX ? 94 : 60; // iPhone X有底部安全区域
    libraryButton.frame = CGRectMake(15, self.view.br_height - bottomMargin, 32, 32);
    [libraryButton setImage:libraryImage forState:UIControlStateNormal];
    libraryButton.contentMode = UIViewContentModeScaleAspectFill;
    [libraryButton addTarget:self action:@selector(openLibrary) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:libraryButton];
}

- (void)setMaskView {
    
    _viewPreview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        _viewPreview.backgroundColor = [UIColor blackColor];
    } else {
        _viewPreview.backgroundColor = [UIColor clearColor];
    }
    [self.view addSubview:_viewPreview];
    
}

- (void)setShadeView {
    
    UIImageView *shadeImgView = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    shadeImgView.image = [self drawMaskImage];
    [self.view addSubview:shadeImgView];
    
}

- (void)setScanWindowView {
    
    // 扫码框宽度为屏幕2/3或300中的较小值
    CGFloat maxWidth = MIN(self.view.br_width * 2.0 / 3.0, 300.0);
    CGFloat scanWindowW = maxWidth;
    CGFloat scanWindowX = (self.view.br_width - scanWindowW) / 2.0; // 居中显示
    
    _scanWindowView = [[UIView alloc] initWithFrame:CGRectMake(scanWindowX, kBorderW, scanWindowW, scanWindowW)];
    _scanWindowView.clipsToBounds = YES;
    [self.view addSubview:_scanWindowView];
    
    _scanNetImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"saoyisao_scan_net"]];
    CGFloat buttonWH = 18;
    
    UIButton *topLeft = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, buttonWH, buttonWH)];
    [topLeft setImage:[UIImage imageNamed:@"saoyisao_scan_1"] forState:UIControlStateNormal];
    [_scanWindowView addSubview:topLeft];
    
    UIButton *topRight = [[UIButton alloc] initWithFrame:CGRectMake(scanWindowW - buttonWH, 0, buttonWH, buttonWH)];
    [topRight setImage:[UIImage imageNamed:@"saoyisao_scan_2"] forState:UIControlStateNormal];
    [_scanWindowView addSubview:topRight];
    
    UIButton *bottomLeft = [[UIButton alloc] initWithFrame:CGRectMake(0, scanWindowW - buttonWH, buttonWH, buttonWH)];
    [bottomLeft setImage:[UIImage imageNamed:@"saoyisao_scan_3"] forState:UIControlStateNormal];
    [_scanWindowView addSubview:bottomLeft];
    
    UIButton *bottomRight = [[UIButton alloc] initWithFrame:CGRectMake(topRight.frame.origin.x, bottomLeft.frame.origin.y, buttonWH, buttonWH)];
    [bottomRight setImage:[UIImage imageNamed:@"saoyisao_scan_4"] forState:UIControlStateNormal];
    [_scanWindowView addSubview:bottomRight];
    
}

- (void)setBottomBarView {
    
    UILabel * tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, _scanWindowView.frame.origin.y+_scanWindowView.br_height, self.view.bounds.size.width, kBorderW)];
    tipLabel.text = @"将取景框对准二维码，即可自动扫描";
    tipLabel.textColor = [UIColor whiteColor];
    tipLabel.textAlignment = NSTextAlignmentCenter;
    tipLabel.lineBreakMode = NSLineBreakByWordWrapping;
    tipLabel.numberOfLines = 2;
    tipLabel.font=[UIFont systemFontOfSize:12];
    tipLabel.backgroundColor = [UIColor clearColor];
    [self.view addSubview:tipLabel];
    
}

- (BOOL)startReading {
    NSError *error;
    
    //1.初始化捕捉设备（AVCaptureDevice），类型为AVMediaTypeVideo
    AVCaptureDevice *captureDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    
    //2.用captureDevice创建输入流
    AVCaptureDeviceInput *input = [AVCaptureDeviceInput deviceInputWithDevice:captureDevice error:&error];
    if (!input) {
        NSLog(@"%@", [error localizedDescription]);
        return NO;
    }
    
    //3.创建媒体数据输出流
    AVCaptureMetadataOutput *captureMetadataOutput = [[AVCaptureMetadataOutput alloc] init];
    
    //4.实例化捕捉会话
    _captureSession = [[AVCaptureSession alloc] init];
    
    //4.1.将输入流添加到会话
    [_captureSession addInput:input];
    
    //4.2.将媒体输出流添加到会话中
    [_captureSession addOutput:captureMetadataOutput];
    
    //5.创建串行队列，并加媒体输出流添加到队列当中
    dispatch_queue_t dispatchQueue;
    dispatchQueue = dispatch_queue_create("myQueue", NULL);
    //5.1.设置代理
    [captureMetadataOutput setMetadataObjectsDelegate:self queue:dispatchQueue];
    
    //5.2.设置输出媒体数据类型为QRCode
    [captureMetadataOutput setMetadataObjectTypes:[NSArray arrayWithObject:AVMetadataObjectTypeQRCode]];
    
    //6.实例化预览图层
    _videoPreviewLayer = [[AVCaptureVideoPreviewLayer alloc] initWithSession:_captureSession];
    
    //7.设置预览图层填充方式
    [_videoPreviewLayer setVideoGravity:AVLayerVideoGravityResizeAspectFill];
    
    //8.设置图层的frame
    [_videoPreviewLayer setFrame:_viewPreview.layer.bounds];
    
    //9.将图层添加到预览view的图层上
    [_viewPreview.layer addSublayer:_videoPreviewLayer];
    
    //10.设置扫描范围
    captureMetadataOutput.rectOfInterest = CGRectMake(0.2f, 0.2f, 0.8f, 0.8f);
    
    //10.开始扫描
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [_captureSession startRunning];
    });
    
    return YES;
}

#pragma mark - AVCaptureMetadataOutputObjectsDelegate
- (void)captureOutput:(AVCaptureOutput *)captureOutput didOutputMetadataObjects:(NSArray *)metadataObjects fromConnection:(AVCaptureConnection *)connection
{
    
    //判断是否有数据
    if (metadataObjects != nil && [metadataObjects count] > 0) {
        
        AVMetadataMachineReadableCodeObject *metadataObj = [metadataObjects objectAtIndex:0];
        //判断回传的数据类型
        if ([[metadataObj type] isEqualToString:AVMetadataObjectTypeQRCode]) {
            
            NSString *resultString = [metadataObj stringValue];
            
            // For medical record export mode, check if it's the target QR code before stopping session
            if (self.shouldShowWebExportHint) {
                // Only stop session if it's a medical record export QR code
                if ([resultString hasPrefix:@"brzy-web://login?"]) {
                    [_captureSession stopRunning];
                    [self scanOrDontScanRequestQrCode:resultString];
                } else {
                    // Not the target QR code, don't stop session, just show error with debounce
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self showInvalidQRCodeErrorForQRCode:resultString];
                    });
                }
            } else {
                // Normal mode: stop session for any QR code
                [_captureSession stopRunning];
                [self scanOrDontScanRequestQrCode:resultString];
            }
        }
    }
}

- (BOOL)shouldAutorotate
{
    return NO;
}

- (UIImage *)drawMaskImage {
    
    CGSize screenSize =[UIScreen mainScreen].bounds.size;
    
    UIGraphicsBeginImageContext(screenSize);
    CGContextRef ctx = UIGraphicsGetCurrentContext();
    CGContextSetRGBFillColor(ctx, 0,0,0,0.5);
    CGRect drawRect =CGRectMake(0, 0, screenSize.width,screenSize.height);
    
    CGContextFillRect(ctx, drawRect);
    
    // 使用与setScanWindowView相同的计算方式
    CGFloat maxWidth = MIN(self.view.br_width * 2.0 / 3.0, 300.0);
    CGFloat scanWindowW = maxWidth;
    CGFloat scanWindowX = (self.view.br_width - scanWindowW) / 2.0;
    drawRect = CGRectMake(scanWindowX, kBorderW, scanWindowW, scanWindowW);
    CGContextClearRect(ctx, drawRect);
    
    
    UIImage* returnimage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return returnimage;
    
}

#pragma mark 恢复动画
- (void)resumeAnimation
{
    CAAnimation *anim = [_scanNetImageView.layer animationForKey:@"translationAnimation"];
    if(anim){
        // 1. 将动画的时间偏移量作为暂停时的时间点
        CFTimeInterval pauseTime = _scanNetImageView.layer.timeOffset;
        // 2. 根据媒体时间计算出准确的启动动画时间，对之前暂停动画的时间进行修正
        CFTimeInterval beginTime = CACurrentMediaTime() - pauseTime;
        
        // 3. 要把偏移时间清零
        [_scanNetImageView.layer setTimeOffset:0.0];
        // 4. 设置图层的开始动画时间
        [_scanNetImageView.layer setBeginTime:beginTime];
        
        [_scanNetImageView.layer setSpeed:1.0];
        
    }else{
        
        CGFloat scanNetImageViewH = 241;
        // 使用与setScanWindowView相同的计算方式
        CGFloat maxWidth = MIN(self.view.br_width * 2.0 / 3.0, 300.0);
        CGFloat scanWindowH = maxWidth;
        CGFloat scanNetImageViewW = _scanWindowView.br_width;
        
        _scanNetImageView.frame = CGRectMake(0, -scanNetImageViewH, scanNetImageViewW, scanNetImageViewH);
        CABasicAnimation *scanNetAnimation = [CABasicAnimation animation];
        scanNetAnimation.keyPath = @"transform.translation.y";
        scanNetAnimation.byValue = @(scanWindowH);
        scanNetAnimation.duration = 1.0;
        scanNetAnimation.repeatCount = MAXFLOAT;
        [_scanNetImageView.layer addAnimation:scanNetAnimation forKey:@"translationAnimation"];
        [_scanWindowView addSubview:_scanNetImageView];
    }
    
}

#pragma mark-> 闪光灯
-(void)openFlash:(UIButton*)button{
    
    NSLog(@"闪光灯");
    button.selected = !button.selected;
    if (button.selected) {
        [self turnTorchOn:YES];
    }
    else{
        [self turnTorchOn:NO];
    }
    
}

#pragma mark-> 开关闪光灯
- (void)turnTorchOn:(BOOL)on
{
    
    Class captureDeviceClass = NSClassFromString(@"AVCaptureDevice");
    if (captureDeviceClass != nil) {
        AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
        
        if ([device hasTorch] && [device hasFlash]){
            
            [device lockForConfiguration:nil];
            if (on) {
                [device setTorchMode:AVCaptureTorchModeOn];
                [device setFlashMode:AVCaptureFlashModeOn];
                
            } else {
                [device setTorchMode:AVCaptureTorchModeOff];
                [device setFlashMode:AVCaptureFlashModeOff];
            }
            [device unlockForConfiguration];
        }
    }
}

#pragma mark - 获取相册内容
- (void)openLibrary {
    
    [self judgeIfHaveAuthorityWithSourceType:UIImagePickerControllerSourceTypePhotoLibrary];
}

#pragma mark - 相册处理
- (void)judgeIfHaveAuthorityWithSourceType:(UIImagePickerControllerSourceType)sourceType {
    
    BOOL cameraAuthority = YES;
    BOOL photoLibraryAuthority = YES;
    
    ALAuthorizationStatus author = [ALAssetsLibrary authorizationStatus];
    if (author == kCLAuthorizationStatusRestricted || author ==kCLAuthorizationStatusDenied)
    {
        //无权限访问相册
        photoLibraryAuthority = NO;
    }
    
    ALAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    if (authStatus == ALAuthorizationStatusRestricted || authStatus ==ALAuthorizationStatusDenied)
    {
        //无权限访问相机
        cameraAuthority = NO;
    }
    
    
    if (photoLibraryAuthority == YES && sourceType == UIImagePickerControllerSourceTypePhotoLibrary) {
        [self gotoPhotoLibraryAndPhotoAlbumWithSourceType:sourceType];
    }
    else if (photoLibraryAuthority == NO && sourceType == UIImagePickerControllerSourceTypePhotoLibrary){
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"确定" forState:UIControlStateNormal];
        [alertView showAlertView:@"请在iPhone的\"设置-隐私-照片\"选项中，允许必然中医访问你的照片" completion:^{
            [alertView close];
        }];
    }
    else if (cameraAuthority == YES && sourceType ==UIImagePickerControllerSourceTypeCamera){
        [self gotoPhotoLibraryAndPhotoAlbumWithSourceType:sourceType];
    }
    else {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"确定" forState:UIControlStateNormal];
        [alertView showAlertView:@"请在iPhone的\"设置-隐私-相机\"选项中，允许必然中医访问你的手机相机" completion:^{
            [alertView close];
        }];
    }
    
}


- (void)gotoPhotoLibraryAndPhotoAlbumWithSourceType:(NSInteger)sourceType {
    
    // 跳转到相机或相册页面
    UIImagePickerController *imagePickerController = [[UIImagePickerController alloc] init];
    imagePickerController.delegate = self;
    imagePickerController.allowsEditing = NO;
    imagePickerController.sourceType = sourceType;
    [self presentViewController:imagePickerController animated:YES completion:^{}];
    
}

#pragma mark - 相册图片会调处理
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<NSString *,id> *)info {
    
    [picker dismissViewControllerAnimated:YES completion:^{
        UIImage *image = [info objectForKey:UIImagePickerControllerEditedImage];
        if (image == nil) {
            image = [info objectForKey:UIImagePickerControllerOriginalImage];
        }
        
        image = [image normalizedImage];
        
        [self processQRCodeFromImage:image];
        
    }];
}


- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - 扫码图片处理
- (void)processQRCodeFromImage:(UIImage *)image {
    CIDetector *detector = [CIDetector detectorOfType:CIDetectorTypeQRCode context:nil options:@{CIDetectorAccuracy: CIDetectorAccuracyHigh}];
    NSArray *features = [detector featuresInImage:[CIImage imageWithCGImage:image.CGImage]];
    
    if (features.count > 0) {
        CIQRCodeFeature *feature = features.firstObject;
        NSString *scannedResult = feature.messageString;
        [self scanOrDontScanRequestQrCode:scannedResult];
    } else {
        [self.view makeToast:@"未检测到二维码" duration:2 position:CSToastPositionCenter];
        // For medical record export mode, add haptic feedback even when no QR code detected
        if (self.shouldShowWebExportHint) {
            [self triggerHapticFeedback];
        }
    }
}

#pragma mark - 扫描取消
- (void)scanOrDontScanRequestQrCode:(NSString *)qrCode
{
    // All UI operations must be on main thread
    dispatch_async(dispatch_get_main_queue(), ^{
        // First, send notification for any scanned QR code (for web login handling)
        [[NSNotificationCenter defaultCenter] postNotificationName:@"QRCodeScanResultNotification" 
                                                            object:nil 
                                                          userInfo:@{@"result": qrCode ?: @""}];
        
        // Check if this is from medical record export (shouldShowWebExportHint = YES)
        if (self.shouldShowWebExportHint) {
            // Medical record export mode: only accept web login QR codes
            if ([qrCode hasPrefix:@"brzy-web://login?"]) {
                // This is a web login QR code, handled by the calling view controller
                [self.navigationController popViewControllerAnimated:YES];
                return;
            } else {
                // Not a medical record export QR code, show error with haptic feedback
                // Note: scanning session continues automatically without restart
                [self showInvalidQRCodeErrorForQRCode:qrCode];
                return;
            }
        }
        
        // Check if this is a web login QR code (for non-export mode)
        if ([qrCode hasPrefix:@"brzy-web://login?"]) {
            // This is a web login QR code, handled by ChatViewController
            [self.navigationController popViewControllerAnimated:YES];
            return;
        }
        
        // Original doctor verification logic for regular QR codes
        NSDictionary *dict = @{@"method_code":@"000263",
                               @"taskId":@"",
                               @"type":@"1",
                               @"reason":@"",
                               @"qrCode":qrCode
                               };
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                [self.view makeToast:@"该医生已成为你的客户" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                    
                    docterID = [responseObject objectForKey:@"code"];
                    [self.navigationController popViewControllerAnimated:YES];
                }];
                
            } else {
                
                [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                    
                    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                        [_captureSession startRunning];
                    });
                    [self.navigationController popViewControllerAnimated:YES];
                }];
            }
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            
            [self.view makeToast:@"网络请求失败，请重试" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                
                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                    [_captureSession startRunning];
                });
                [self.navigationController popViewControllerAnimated:YES];
            }];
        }];
    });
}

#pragma mark - Helper Methods for Medical Record Export Mode

- (void)showInvalidQRCodeError {
    [self showInvalidQRCodeErrorForQRCode:nil];
}

- (void)showInvalidQRCodeErrorForQRCode:(NSString *)qrCode {
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval cooldownPeriod = 2.0; // 2秒冷却时间
    
    // 检查是否是相同的二维码且在冷却时间内
    if (lastInvalidQRCode && qrCode && 
        [lastInvalidQRCode isEqualToString:qrCode] && 
        (currentTime - lastInvalidQRCodeTime) < cooldownPeriod) {
        // 在冷却时间内，不显示重复提示
        return;
    }
    
    // 更新记录
    lastInvalidQRCode = qrCode ? [qrCode copy] : nil;
    lastInvalidQRCodeTime = currentTime;
    
    // Show toast message
    [self.view makeToast:@"这不是医案导出二维码" duration:2 position:CSToastPositionCenter];
    
    // Add haptic feedback
    [self triggerHapticFeedback];
}

- (void)triggerHapticFeedback {
    // Use impact feedback for error indication
    if (@available(iOS 10.0, *)) {
        UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
        [feedbackGenerator prepare];
        [feedbackGenerator impactOccurred];
    }
}

@end
