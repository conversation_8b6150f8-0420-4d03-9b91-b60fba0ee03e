//
//  BRComPrescriptionViewController.m
//  BRZY
//
//  Created by <PERSON><PERSON><PERSON> han on 2017/11/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRComPrescriptionViewController.h"
#import "BRComView.h"
#import "BRHistoryView.h"
#import "Reachability.h"
#import "BRPresInfoView.h"
#import "BRPresReplaceDrugView.h"
#import "BRAlertView.h"
#import "BRPresInfoModel.h"
#import "MJRefresh.h"
#import "TitleBarView.h"
#import "ClassicPrescriptionViewController.h"
#import "ClassicPrescriptionSearchViewController.h"
#import "AddDrugViewController.h"
#import "BRPresNoticeView.h"

@interface BRComPrescriptionViewController ()<UIScrollViewDelegate,ClassicPresciptionVCDelegate>

@property (nonatomic, strong) TitleBarView   *titleBarView;
@property (nonatomic, strong) UIView   *lineView;
@property (nonatomic, strong) UIButton *comBtn;
@property (nonatomic, strong) UIButton *historyBtn;
@property (nonatomic, strong) BRComView *comView;
@property (nonatomic, strong) BRHistoryView *historyView;
@property (nonatomic, strong) Reachability *reachability;
@property (nonatomic, strong) BRPresInfoView *presInfoView;//长按显示处方详情
@property (nonatomic, strong) BRPresReplaceDrugView *replaceView;//替换药

@property (nonatomic, strong) NSArray *comDataArr;
@property (nonatomic, strong) NSMutableArray *historyDataArr;
@property (nonatomic, assign) BOOL isFirstHistory;       //第一次点击历史药方时请求数据
@property (nonatomic, strong) NSMutableArray *selectDrugArr;//选中的药材列表

@property (nonatomic, assign) BOOL historyIsAll;   //是否是全部患者

@property (nonatomic, assign) int requestPageNum;
@property (nonatomic, assign) BOOL isLoadData;
@property (nonatomic, assign) BOOL requestIsAll;

@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, strong) UIView *commonPrescriptionView;
@property (nonatomic, strong) UIView *historyPrescriptionView;

@property (nonatomic, strong) BRPresNoticeView *presNoticeView; // 提示标语视图

@end

@implementation BRComPrescriptionViewController

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBar.hidden = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    if (@available(iOS 11.0, *)) {
        
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    self.title = @"药方模板";
    
    [self showNavBackItem];
    [self showNavSearchItem];
    
    self.historyDataArr = [NSMutableArray array];
    _selectDrugArr = [[NSMutableArray alloc]init];
    
    TitleBarView *titleBar = [[TitleBarView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kCellDefaultHeight) andTitles:@[@"经典方",@"常用方",@"历史药方"]];
    titleBar.isShowBottomLine = YES;
    titleBar.backgroundColor = [UIColor whiteColor];
    titleBar.leftOrRightSpace = (isiPhone4 || isiPhone5) ? 40 : 60;
    titleBar.originColor = [UIColor blackColor];
    [self.view addSubview:titleBar];
    
    _titleBarView = titleBar;
    
    titleBar.bottomLineView.backgroundColor = [UIColor colorWithHex:0xeaeaea];
    
    // 初始化提示视图
    _presNoticeView = [[BRPresNoticeView alloc] init];
    _presNoticeView.noticeStr = @"长按方名可调整药味后再使用";
    _presNoticeView.hidden = YES; // 初始隐藏
    [self.view addSubview:_presNoticeView];
    
    __weak typeof(self) weakSelf = self;
    _presNoticeView.closeButtonBlock = ^{
        [weakSelf hideNoticeViewAndSaveState];
    };
    
    // 设置提示视图约束
    [_presNoticeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleBar.mas_bottom);
        make.left.and.right.equalTo(weakSelf.view);
        make.height.mas_equalTo(0); // 初始高度为0
    }];
    
    self.isFirstHistory = YES;
    
    _scrollView = [[UIScrollView alloc] init];
    _scrollView.delegate = self;
    _scrollView.pagingEnabled = YES;
    [self.view addSubview:_scrollView];
    
    // 设置 scrollView 约束
    [_scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(weakSelf.presNoticeView.mas_bottom);
        make.left.right.bottom.equalTo(weakSelf.view);
    }];
    
    // 延迟设置 contentSize，等待布局完成
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakSelf.scrollView setContentSize:CGSizeMake(SCREEN_WIDTH * 3, weakSelf.scrollView.bounds.size.height)];
    });
    
    //经典方
    ClassicPrescriptionViewController *classicVC = [[ClassicPrescriptionViewController alloc] init];
    classicVC.delegate = self;
    [self addChildViewController:classicVC];
    
    [_scrollView addSubview:classicVC.view];
    
    // 设置经典方视图约束
    [classicVC.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(weakSelf.scrollView);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.equalTo(weakSelf.scrollView);
    }];
    
    
//    __weak BRComPrescriptionViewController *weakSelf = self;
    titleBar.titleButtonClicked = ^(NSUInteger index) {
        [weakSelf.scrollView setContentOffset:CGPointMake(index * SCREEN_WIDTH, 0)];
        //经典方
        if (index == 0) {
            [self showNavSearchItem];
            [weakSelf showNoticeViewForPageType:@"classic"]; // 显示经典方提示
        }
        //常用方
        else if (index == 1) {
            [weakSelf requestData];
            [weakSelf hideNavSearchItem];
            [weakSelf showNoticeViewForPageType:@"common"]; // 显示常用方提示
        }
        //历史药方
        else if (index == 2) {
            [weakSelf requestHistoryData];
            
            [weakSelf hideNavSearchItem];
            [weakSelf hideNoticeView]; // 隐藏提示
        }
    };
    
    titleBar.currentIndex = 1;
    
    self.commonPrescriptionView = [[UIView alloc] init];
    [_scrollView addSubview:self.commonPrescriptionView];
    
    // 设置常用方视图约束
    [self.commonPrescriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(classicVC.view.mas_right);
        make.top.bottom.equalTo(weakSelf.scrollView);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.equalTo(weakSelf.scrollView);
    }];
    
    self.historyPrescriptionView = [[UIView alloc] init];
    [_scrollView addSubview:self.historyPrescriptionView];
    
    // 设置历史药方视图约束
    [self.historyPrescriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(weakSelf.commonPrescriptionView.mas_right);
        make.top.bottom.equalTo(weakSelf.scrollView);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.equalTo(weakSelf.scrollView);
        make.right.equalTo(weakSelf.scrollView); // 设置右边界，确定 scrollView 的 contentSize
    }];
    
    
//    [self requestData];
    [self createUI];
    
    // 由于默认显示常用方，设置 scrollView 的初始位置并执行相应逻辑
    dispatch_async(dispatch_get_main_queue(), ^{
        // 设置 scrollView 滚动到常用方页面
        [weakSelf.scrollView setContentOffset:CGPointMake(1 * SCREEN_WIDTH, 0) animated:NO];
        
        // 执行常用方的初始化逻辑
        [weakSelf requestData];
        [weakSelf hideNavSearchItem];
        [weakSelf showNoticeViewForPageType:@"common"];
    });
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(classicPrescriptionDealWithInfo:) name:kNotificationDealSearchClassicPrescription object:nil];
}

- (void)clickBackButton:(UIButton *)sender {
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kNotificationDealSearchClassicPrescription object:nil];
    [self.navigationController popViewControllerAnimated:YES];
}

//请求常用方
- (void)requestData {
    
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000203",@"method_code", nil];
    [dic setObject:[[UserManager shareInstance] getUserId] forKey:@"userId"];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                if (![[dataDic objectForKey:@"data"] isKindOfClass:[NSNull class]]) {
                    
                    mySelf.comDataArr = [dataDic objectForKey:@"data"];
                    [mySelf createUI];
                    
                } else {
                    
                    [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
                }
                
            } else {
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [mySelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                
            }
            
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            [mySelf createUI];
            [mySelf.comView createNoWIFIView];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
                
            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
                
            }
        }
    }];
}
//弹出框
- (void)createAlert:(NSString *)title {
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    
    __weak typeof(alertView) aView = alertView;
    [alertView showAlertView:title completion:^{
        [aView close];
    }];
}

- (void)createUI {
    
    NSMutableArray *arr = [NSMutableArray array];
    
    if (self.comDataArr) {
        for (NSDictionary *dic in self.comDataArr) {
            NSString *nameStr = @"";
            if (![[dic objectForKey:@"name"] isKindOfClass:[NSNull class]]) {
                NSString *str = [dic objectForKey:@"name"];
                if (str.length) {
                    nameStr = str;
                }
            }
            [arr addObject:[nameStr copy]];
        }
    }
    
    __weak typeof(self) mySelf = self;
    self.comView = [[BRComView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kScreenHeight - kCellDefaultHeight - kTabbarSafeBottomMargin - kStatusBarHeight - kTopBarHeight) withData:arr dataBack:^(NSUInteger num) {
        //点击常用方
        [mySelf comClick:num];

    }];
    self.comView.reloadData = ^{

        [mySelf requestData];

    };
//    [self.view addSubview:self.comView];
    [self.commonPrescriptionView addSubview:self.comView];
    
#pragma mark - 常用方长按事件
    self.comView.longPressBlock = ^(UIButton *btn) {
        NSInteger comNum = btn.tag - 50;
        //comNum 为点击的第几个常用方，排列顺序为每行从左往右,行数从上到下 (现代书写方式顺序)
        [mySelf comLongClick:comNum];
    };
    
    
    
    self.historyView = [[BRHistoryView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kScreenHeight - kCellDefaultHeight - kTabbarSafeBottomMargin - kStatusBarHeight - kTopBarHeight) withDataBlock:^(NSUInteger num) {
        
        [mySelf historyClick:num];
        
    } refresh:^(BOOL isRefresh) {
        
        if (isRefresh) {
            //刷新
            [mySelf refreshData];
            
        } else {
            //加载更多
            [mySelf reloadData];
        }
        
        [mySelf requestHistoryData];
        
    }];
    self.historyView.changeBlock = ^(BOOL isAll) {
        mySelf.historyIsAll = isAll;
        [mySelf refreshData];
        [mySelf requestHistoryData];
    };
    
    self.historyView.reloadData = ^{
      
        _requestPageNum = 0;
        [mySelf requestHistoryData];
        
    };
    
    [self.historyPrescriptionView addSubview:self.historyView];
    
//    [self.view addSubview:self.historyView];
    
//    [self.view insertSubview:self.comView aboveSubview:self.historyView];
}

- (void)refreshData {
    [self.historyDataArr removeAllObjects];
    self.requestPageNum = 0;
    self.isLoadData = NO;
}

- (void)reloadData {
    self.isLoadData = YES;
}

#pragma mark - 顶部视图
- (UIView *)topView:(CGRect)frame {
    UIView *view = [[UIView alloc] initWithFrame:frame];
    view.backgroundColor = [UIColor whiteColor];
    
    CGSize size = [@"常用方" boundingRectWithSize:CGSizeMake(200, 40) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Regular(16)} context:nil].size;
    CGSize size2 = [@"历史药方" boundingRectWithSize:CGSizeMake(200, 40) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Regular(16)} context:nil].size;
    self.comBtn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    [self.comBtn setTitle:@"常用方" forState:UIControlStateNormal];
    self.comBtn.frame = CGRectMake(view.frame.size.width/2-FONTSIZE(20)-size.width, 0, size.width, view.frame.size.height);
    self.comBtn.tag = 51;
    [self.comBtn setTitleColor:[UIColor colorWithRed:65.0/255 green:133.0/255 blue:220.0/255 alpha:1.f] forState:UIControlStateNormal];
    self.comBtn.titleLabel.font = FONT_Regular(16);
    [self.comBtn addTarget:self action:@selector(comBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [view addSubview:self.comBtn];
    
    self.historyBtn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    [self.historyBtn setTitle:@"历史药方" forState:UIControlStateNormal];
    self.historyBtn.frame = CGRectMake(view.frame.size.width/2+FONTSIZE(20), 0, size2.width, view.frame.size.height);
    self.historyBtn.tag = 50;
    [self.historyBtn setTitleColor:[UIColor colorWithRed:29.0/255 green:32.0/255 blue:36.0/255 alpha:1.f] forState:UIControlStateNormal];
    self.historyBtn.titleLabel.font = FONT_Regular(16);
    [self.historyBtn addTarget:self action:@selector(historyBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [view addSubview:self.historyBtn];
    
    UIView *lineView1 = [[UIView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, 1)];
    lineView1.backgroundColor = [UIColor colorWithRed:234.0/255 green:234.0/255 blue:234.0/255 alpha:1.f];
    [view addSubview:lineView1];
    
    UIView *lineView2 = [[UIView alloc] initWithFrame:CGRectMake(0, view.frame.size.height-1, view.frame.size.width, 1)];
    lineView2.backgroundColor = [UIColor colorWithRed:246.0/255 green:246.0/255 blue:246.0/255 alpha:1.f];
    [view addSubview:lineView2];
    
    self.lineView = [[UIView alloc] initWithFrame:CGRectMake(self.comBtn.frame.origin.x, view.frame.size.height-1-FONTSIZE(5), size.width, FONTSIZE(5))];
    self.lineView.backgroundColor = [UIColor colorWithRed:65.0/255 green:133.0/255 blue:220.0/255 alpha:1.f];
    self.lineView.layer.masksToBounds = YES;
    self.lineView.layer.cornerRadius = FONTSIZE(3);
    [view addSubview:self.lineView];
    
    return view;
}
//常用方点击
- (void)comBtnClick:(UIButton *)btn {
    if (btn.tag == 50) {
        self.comBtn.tag = 51;
        [self.comBtn setTitleColor:[UIColor colorWithRed:65.0/255 green:133.0/255 blue:220.0/255 alpha:1.f] forState:UIControlStateNormal];
        self.historyBtn.tag = 50;
        [self.historyBtn setTitleColor:[UIColor colorWithRed:29.0/255 green:32.0/255 blue:36.0/255 alpha:1.f] forState:UIControlStateNormal];
        __weak typeof(self)mySelf = self;
        self.historyBtn.userInteractionEnabled = NO;
        [UIView animateWithDuration:0.2 animations:^{
            
            mySelf.lineView.frame = CGRectMake(mySelf.comBtn.frame.origin.x, mySelf.lineView.frame.origin.y, self.comBtn.frame.size.width, FONTSIZE(5));
            
        } completion:^(BOOL finished) {
            mySelf.historyBtn.userInteractionEnabled = YES;
        }];
        
//        [self.view insertSubview:self.comView aboveSubview:self.historyView];
    }
}
//历史药方点击
- (void)historyBtnClick:(UIButton *)btn {
    if (btn.tag == 50) {
        self.historyBtn.tag = 51;
        [self.historyBtn setTitleColor:[UIColor colorWithRed:65.0/255 green:133.0/255 blue:220.0/255 alpha:1.f] forState:UIControlStateNormal];
        self.comBtn.tag = 50;
        [self.comBtn setTitleColor:[UIColor colorWithRed:29.0/255 green:32.0/255 blue:36.0/255 alpha:1.f] forState:UIControlStateNormal];
        __weak typeof(self)mySelf = self;
        self.comBtn.userInteractionEnabled = NO;
        [UIView animateWithDuration:0.2 animations:^{
            
            mySelf.lineView.frame = CGRectMake(mySelf.historyBtn.frame.origin.x, mySelf.lineView.frame.origin.y, self.historyBtn.frame.size.width, FONTSIZE(5));
            
        } completion:^(BOOL finished) {
            mySelf.comBtn.userInteractionEnabled = YES;
        }];
        
//        [self.view insertSubview:self.historyView aboveSubview:self.comView];
        if (self.isFirstHistory) {
            [self refreshData];
            [self requestHistoryData];
        }
    }
}
//请求历史药方
- (void)requestHistoryData {
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000205",@"method_code", nil];
    [dic setObject:[[UserManager shareInstance] getUserId] forKey:@"userId"];
    if (!self.historyIsAll) {
        if (self.patientId.length > 0) {
            [dic setObject:self.patientId forKey:@"patientId"];
        }
        else {
            [dic setObject:@"0" forKey:@"patientId"];
        }
//        else {
//            [self.view makeToast:@"数据出错，orderId为空" duration:2 position:CSToastPositionCenter];
//            return;
//        }
    }
    [dic setObject:self.drugType forKey:@"drugType"];
    [dic setObject:self.drugProviderId forKey:@"drugProviderId"];
    
    self.requestPageNum ++;
    self.requestIsAll = NO;
    
    [dic setObject:[NSString stringWithFormat:@"%d",self.requestPageNum] forKey:@"pageNum"];
    [dic setObject:@"20" forKey:@"pageSize"];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        
        NSLog(@"history response data list = %@",responseObject);
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                mySelf.isFirstHistory = NO;
                if (![[dataDic objectForKey:@"data"] isKindOfClass:[NSNull class]]) {
                    
                    [mySelf.historyDataArr addObjectsFromArray:[dataDic objectForKey:@"data"]];
                    
                    [mySelf.historyView refreshViewWithData:mySelf.historyDataArr];
                    
                } else {
                    
                    [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
                }
                
//                mySelf.requestPageNum = [[dataDic objectForKey:@"pageNum"] intValue];
                if ([[dataDic objectForKey:@"pageNum"] intValue] >= [[dataDic objectForKey:@"totalPage"] intValue]) {
                    mySelf.requestIsAll = YES;
                }
                
            } else {
                
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [mySelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        if (mySelf.isLoadData) {
            
            if (mySelf.requestIsAll) {
                mySelf.historyView.tableView.mj_footer.state = MJRefreshStateNoMoreData;
            } else {
                [mySelf.historyView.tableView.mj_footer endRefreshing];
            }
        } else {
            
            [mySelf.historyView.tableView.mj_header endRefreshing];
            if (mySelf.requestIsAll) {
                mySelf.historyView.tableView.mj_footer.state = MJRefreshStateNoMoreData;
            } else {
                mySelf.historyView.tableView.mj_footer.state = MJRefreshStateIdle;
            }
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        if (mySelf.isLoadData) {
            [mySelf.historyView.tableView.mj_footer endRefreshing];
            
        } else {
            [mySelf.historyView.tableView.mj_header endRefreshing];
            
        }
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            [mySelf.historyView createNoWIFIView];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"请求超时。"]) {
                //请求超时
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
                
            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
                
            }
        }
    }];
}

#pragma mark- 请求处方详情
- (void)requestPresInfoWithTouchType:(NSString *)touchType comId:(NSString *)comId{
    
    NSDictionary *requestDict = @{
                                  @"method_code":@"000204",
                                  @"drugType":_drugType,
                                  @"preTemplateId":comId,
                                  @"drugProviderId":_drugProviderId,
                                  @"drugForm":_drugForm
                                  };
    
    __weak BRComPrescriptionViewController *weakSelf = self;
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:requestDict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        hud.hidden = YES;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            BRPresInfoModel *infoModel = [[BRPresInfoModel alloc]init];
            infoModel.preTemplateId = [responseObject objectForKey:@"preTemplateId"];
            infoModel.preTemplateName = [responseObject objectForKey:@"preTemplateName"];

            infoModel.drugList = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray: [responseObject objectForKey:@"data"]];
            

            for (BRSubMedicineModel *replaceModel in infoModel.drugList) {
                
                NSArray *replaceDataArr = (NSArray *)replaceModel.replaceData;
                if (replaceDataArr.count > 0) {
                    
                    NSArray *replaceableDrugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:replaceDataArr];
                    replaceModel.replaceData = replaceableDrugArr;
                    
                }
                
            }
            
            [_selectDrugArr removeAllObjects];
            [_selectDrugArr addObjectsFromArray:infoModel.drugList];
            
            if ([touchType isEqualToString:@"longPress"]) {
                
                //显示药方详情
                [weakSelf showPrescriptionInfoWithDrugArr:infoModel.drugList presName:infoModel.preTemplateName];
                
            }
            else {
                
                //直接显示替换药
                [weakSelf judgeIfShowReplaceDrugViewWithDrugArr:infoModel.drugList];
                
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        hud.hidden = YES;
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}
#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    int index = scrollView.contentOffset.x / SCREEN_WIDTH;
    
    _titleBarView.currentIndex = index;
    
    // 根据当前页面显示或隐藏提示
    if (index == 0) {
        // 经典方，显示提示
        [self showNoticeViewForPageType:@"classic"];
    } else if (index == 1) {
        // 常用方，显示提示
        [self showNoticeViewForPageType:@"common"];
    } else if (index == 2) {
        // 历史药方，隐藏提示
        [self hideNoticeView];
    }
}
#pragma mark - 点击搜索
- (void)clickZoomButton:(UIButton *)sender {
    ClassicPrescriptionSearchViewController *classicSearchVC = [[ClassicPrescriptionSearchViewController alloc] init];
    classicSearchVC.addDrugViewController = self.addDrugViewController;
    [self.navigationController pushViewController:classicSearchVC animated:YES];
}
#pragma mark - 点击常用方
- (void)comClick:(NSUInteger)num {
    NSDictionary *dic = self.comDataArr[num];
    NSString *comId = @"";
    if (![[dic objectForKey:@"id"] isKindOfClass:[NSNull class]]) {
        comId = [dic objectForKey:@"id"];
    }
    
    [self requestPresInfoWithTouchType:@"unlongPress" comId:comId];
    
}
#pragma mark - 长按常用方
- (void)comLongClick:(NSUInteger)num {
    NSDictionary *dic = self.comDataArr[num];
    NSString *comId = @"";
    if (![[dic objectForKey:@"id"] isKindOfClass:[NSNull class]]) {
        comId = [dic objectForKey:@"id"];
    }
    
    [self requestPresInfoWithTouchType:@"longPress" comId:comId];
    
}
#pragma mark - 经典方点击处理
- (void)classicPrescriptionSelectedIsLongPress:(BOOL)isLongPrss pId:(NSString *)pId name:(NSString *)name {
    if (isLongPrss) {
        [self requestClassicWithTouchType:@"longPress" comId:pId name:name];
    }
    else {
        [self requestClassicWithTouchType:@"unlongPress" comId:pId name:name];
    }
}

- (void)classicPrescriptionDealWithInfo:(NSNotification *)notify {
    NSDictionary *dict = notify.userInfo;
    
    [self requestClassicWithTouchType:[dict objectForKey:@"touchType"] comId:[dict objectForKey:@"pId"] name:[dict objectForKey:@"name"]];
}

- (void)requestClassicWithTouchType:(NSString *)touchType comId:(NSString *)comId name:(NSString *)name{
    
    NSDictionary *requestDict = @{
                                  @"method_code":@"000309",
                                  @"drugType":_drugType,
                                  @"recipeId":comId,
                                  @"drugProviderId":_drugProviderId,
                                  @"drugForm":_drugForm
                                  };
    
    __weak BRComPrescriptionViewController *weakSelf = self;
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:requestDict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        hud.hidden = YES;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            BRPresInfoModel *infoModel = [[BRPresInfoModel alloc]init];
            infoModel.preTemplateId = [responseObject objectForKey:@"preTemplateId"];
            infoModel.preTemplateName = [responseObject objectForKey:@"preTemplateName"];
            
            infoModel.drugList = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray: [responseObject objectForKey:@"data"]];
            
            
            for (BRSubMedicineModel *replaceModel in infoModel.drugList) {
                
                NSArray *replaceDataArr = (NSArray *)replaceModel.replaceData;
                if (replaceDataArr.count > 0) {
                    
                    NSArray *replaceableDrugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:replaceDataArr];
                    replaceModel.replaceData = replaceableDrugArr;
                    
                }
                
            }
            
            [_selectDrugArr removeAllObjects];
            [_selectDrugArr addObjectsFromArray:infoModel.drugList];
            
            if ([touchType isEqualToString:@"longPress"]) {
                
                //显示药方详情
                [weakSelf showPrescriptionInfoWithDrugArr:infoModel.drugList presName:name];
                
            }
            else {
                
                //直接显示替换药
                [weakSelf judgeIfShowReplaceDrugViewWithDrugArr:infoModel.drugList];
                
            }
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        hud.hidden = YES;
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
    
}
#pragma mark - 点击历史药方
- (void)historyClick:(NSUInteger)num {
    
    NSDictionary *dict = self.historyDataArr[num];
    
    [self requestHistoryInfoWithPreDict:dict];
    
}
//历史药方详情
- (void)requestHistoryInfoWithPreDict:(NSDictionary *)preDict {
    
    NSDictionary *dict = @{
                           @"method_code":@"000271",
                           @"preId":[preDict objectForKey:@"preId"],
                           @"drugType":_drugType,
                           @"drugProviderId":_drugProviderId,
                           @"drugForm":_drugForm
                           };
    
    __weak BRComPrescriptionViewController *weakSelf = self;
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        hud.hidden = YES;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            NSString *preName = [preDict objectForKey:@"preName"];
            
            NSArray *drugList = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:[responseObject objectForKey:@"data"]];
            
            for (BRSubMedicineModel *replaceModel in drugList) {
                
                NSArray *replaceDataArr = (NSArray *)replaceModel.replaceData;
                if (replaceDataArr.count > 0) {
                    
                    NSArray *replaceableDrugArr = [BRSubMedicineModel mj_objectArrayWithKeyValuesArray:replaceDataArr];
                    replaceModel.replaceData = replaceableDrugArr;
                    
                }
                
            }
            
            [_selectDrugArr removeAllObjects];
            [_selectDrugArr addObjectsFromArray:drugList];
            
            //直接显示替换药
            [weakSelf judgeIfShowReplaceDrugViewWithDrugArr:drugList];
            
        }
        else {
            
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
            
        }
        
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        hud.hidden = YES;
        [weakSelf.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
        
    }];
    
}

#pragma mark 长按显示处方详情
- (void)showPrescriptionInfoWithDrugArr:(NSArray *)drugArr presName:(NSString *)presName {
    
    _presInfoView = [[BRPresInfoView alloc]init];
    _presInfoView.drugArr = [NSMutableArray arrayWithArray:drugArr];
    _presInfoView.title = presName;
    _presInfoView.bottomTitle = @"确定";
    _presInfoView.topButtonTitle = @"清空";
    [_presInfoView show];
    
    __weak BRPresInfoView *weakPresInfoView = _presInfoView;
    __weak BRComPrescriptionViewController *weakSelf = self;
    _presInfoView.clickBottomButton = ^(NSMutableArray *selectDrugArr) {
        
        [weakPresInfoView close];
        
        [weakSelf.selectDrugArr removeAllObjects];
        [weakSelf.selectDrugArr addObjectsFromArray:selectDrugArr];
        [weakSelf judgeIfShowReplaceDrugViewWithDrugArr:selectDrugArr];
        
    };
    
}

#pragma mark 显示 替换药
- (void)showPrescriptionReplaceDrugViewWithRepalceDrugArr:(NSMutableArray *)replaceDrugArr {
    
    //显示替换药
    BRPresReplaceDrugView *replaceView = [[BRPresReplaceDrugView alloc]init];
    replaceView.title = @"替换药提示";
    replaceView.bottomTitle = @"确定";
    _replaceView = replaceView;
    replaceView.replaceDrugArr = replaceDrugArr;
    [replaceView show];
    
    __weak BRComPrescriptionViewController *weakSelf = self;
    replaceView.clickBottomButton = ^(NSArray *replaceDrugArr) {
        
        [weakSelf replaceDrugWithReplaceDrugArr:replaceDrugArr];
        
    };
    
}

#pragma mark 判断是否显示替换药
- (void)judgeIfShowReplaceDrugViewWithDrugArr:(NSArray *)drugArr {
    
    if (![_selfSupport isEqualToString:@"Y"] && ![_drugType isEqualToString:@"K"]) {
        
        //替换药逻辑只在配方颗粒和自营饮片时用（包括其他剂型）
        [self gotoAddDrugVCWithNewDrugArr:drugArr];
        return;
        
    }
    
    NSMutableArray *replaceDrugArr = [[NSMutableArray alloc]init];
    for (BRSubMedicineModel *model in drugArr) {
        
        NSArray *replaceableArr = model.replaceData;
        if (replaceableArr.count > 0) {
            [replaceDrugArr addObject:model];
        }
        
    }
    
    if (replaceDrugArr.count > 0) {
        
        //显示替换药
        [self showPrescriptionReplaceDrugViewWithRepalceDrugArr:replaceDrugArr];
        
    }
    else {
        
        [self gotoAddDrugVCWithNewDrugArr:drugArr];
        
        
    }
    
}

#pragma mark 真正的替换药
- (void)replaceDrugWithReplaceDrugArr:(NSArray *)replaceDrugArr {
    
    /**
     * 这是剔除库存不足药材的逻辑
    NSMutableArray *newReplaceDrugArr = [[NSMutableArray alloc]initWithArray:_selectDrugArr];
    for (NSInteger index=0; index<_selectDrugArr.count; index++) {
        
        BRSubMedicineModel *drugModel = [_selectDrugArr objectAtIndex:index];
        if (drugModel.replaceData) {
            
            for (BRSubMedicineModel *replaceModel in replaceDrugArr) {
                
                if ([replaceModel.replaceDrugId isEqualToString:drugModel.drugId]) {
                    
                    [newReplaceDrugArr replaceObjectAtIndex:index withObject:replaceModel];
                    
                }
                
            }
            
        }
        
    }
    
    NSMutableArray *newDrugArr = [[NSMutableArray alloc]init];
    for (NSInteger index=0; index<newReplaceDrugArr.count; index++) {
        
        BRSubMedicineModel *model = [newReplaceDrugArr objectAtIndex:index];
        NSArray *replaceData = model.replaceData;
        if (replaceData.count == 0) {
            [newDrugArr addObject:model];
        }
        
    }
     */
    
    NSMutableArray *newReplaceDrugArr = [[NSMutableArray alloc]initWithArray:_selectDrugArr];
    for (NSInteger index=0; index<_selectDrugArr.count; index++) {
        
        BRSubMedicineModel *drugModel = [_selectDrugArr objectAtIndex:index];
        if (drugModel.replaceData) {
            
            for (BRSubMedicineModel *replaceModel in replaceDrugArr) {
                
                if ([replaceModel.replaceDrugId isEqualToString:drugModel.drugId]) {
                    
                    replaceModel.dose = replaceModel.minSaleUnit;
                    [newReplaceDrugArr replaceObjectAtIndex:index withObject:replaceModel];
                    
                }
                
            }
            
        }
        
    }
    
    [self.replaceView close];
    
    [self gotoAddDrugVCWithNewDrugArr:newReplaceDrugArr];
    
}

- (void)gotoAddDrugVCWithNewDrugArr:(NSArray *)newDrugArr {
    
    if ([self.delegate respondsToSelector:@selector(AddDrugFromSelectDrugArr:)]) {
        [[NSNotificationCenter defaultCenter] removeObserver:self name:kNotificationDealSearchClassicPrescription object:nil];
        [self.delegate AddDrugFromSelectDrugArr:newDrugArr];
    }
    
    if ([Utils isCurrentViewControllerVisible:self]) {
        [self clickBackButton:nil];
    }
    else {
        [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationSearchViewPop object:nil];
    } 
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - 提示视图显示/隐藏
- (void)showNoticeViewForPageType:(NSString *)pageType {
    // 检查该页面类型的提示是否已被用户关闭
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    NSString *key = nil;
    
    if ([pageType isEqualToString:@"classic"]) {
        key = kUserDefaultClassicPrescriptionNoticeHidden;
    } else if ([pageType isEqualToString:@"common"]) {
        key = kUserDefaultCommonPrescriptionNoticeHidden;
    }
    
    if (key && [userDefaults boolForKey:key]) {
        // 该页面的提示已被用户关闭，不显示
        [self hideNoticeView];
        return;
    }
    
    // 显示提示
    if (_presNoticeView.hidden) {
        _presNoticeView.hidden = NO;
        
        CGFloat height = [_presNoticeView getNoticeViewHeight];
        
        [_presNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height);
        }];
        
        [self.view layoutIfNeeded];
    }
}

- (void)hideNoticeView {
    if (!_presNoticeView.hidden) {
        [_presNoticeView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
        
        [self.view layoutIfNeeded];
        self.presNoticeView.hidden = YES;
    }
}

- (void)hideNoticeViewAndSaveState {
    // 获取当前页面类型
    int currentIndex = _titleBarView.currentIndex;
    NSString *pageType = nil;
    NSString *key = nil;
    
    if (currentIndex == 0) {
        // 经典方
        pageType = @"classic";
        key = kUserDefaultClassicPrescriptionNoticeHidden;
    } else if (currentIndex == 1) {
        // 常用方
        pageType = @"common";
        key = kUserDefaultCommonPrescriptionNoticeHidden;
    }
    
    // 保存用户关闭状态
    if (key) {
        NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
        [userDefaults setBool:YES forKey:key];
        [userDefaults synchronize];
    }
    
    // 隐藏提示视图
    [self hideNoticeView];
}

@end
