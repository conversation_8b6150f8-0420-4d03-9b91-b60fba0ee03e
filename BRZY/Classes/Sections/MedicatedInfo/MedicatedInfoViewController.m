//
//  MedicatedInfoViewController.m
//  BRZY
//
//  Created by <PERSON><PERSON><PERSON> han on 2017/11/27.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "MedicatedInfoViewController.h"
#import "QualificationCertificationViewController.h"

#import "Reachability.h"
#import "BRAlertView.h"
#import "BRNoDataView.h"
#import "BRAlertView.h"
#import "UIScrollView+EmptyDataSet.h"
#import "DoctorAuthentiationModel.h"

#import "MWPhotoBrowser.h"
#import "BRCanTapImgView.h"
#import "DrugDetailListViewController.h"
#import "WXApi.h"

@interface MedicatedInfoViewController () <UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate,DZNEmptyDataSetSource,DZNEmptyDataSetDelegate,MWPhotoBrowserDelegate> {
    NSArray *_titleTextArr;
    NSString *_bianzhengStr;
    CGSize _bianzhengSize;
    NSString *_anyuStr;
    CGSize _anyuSize;
    
    NSMutableArray *_medicatedArr;
    UIView *_medicatedView;
    UIView *_medicatedInfoView;

}

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) Reachability *reachability;
@property (nonatomic, strong) UIView *viewHeader;

@property (nonatomic, strong) UIView        *backview;
@property (nonatomic, strong) UIView        *teamView;
@property (nonatomic, strong) UITextField   *numberTF;

@property (nonatomic, strong) BRNoDataView *noDataView;

@property (nonatomic, strong) UIButton *refreshBtn; //修改剂数按钮
@property (nonatomic, strong) UIButton *removeBtn;  //作废按钮
@property (nonatomic, strong) UIButton *prescriptionCopyBtn; //复制药方到添加药材按钮
@property (nonatomic, strong) MBProgressHUD *hud;

@property (nonatomic, copy) NSString *backColor;

@property (nonatomic, strong) NSArray *detailsArray;

@property (nonatomic, strong) UIImage *presPhoto; //原拍方照片
@property (nonatomic, assign) BRPrescriptionType prescriptionType;
@property (nonatomic, strong) MWPhotoBrowser *browser;

@property (nonatomic, copy) NSString *isSecurity;
@end

@implementation MedicatedInfoViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor br_backgroundColor];
    [self creatNavItem];
    
    _prescriptionType = BRPrescriptionTypeOnline;
    
    if (self.medicatedInfoOrPreview == NSMedicatedInfo) {
        [self requestData];
    } else {
        [self createUI:NO];
    }
}

- (void)creatNavItem {
    __weak typeof(self)mySelf = self;
    NSString *titleStr = @"用药详情";
    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
        titleStr = @"用药预览";
    }
    [self creatLeftAndTitleNavItemwithTitle:titleStr backClick:^{
        [mySelf.navigationController popViewControllerAnimated:YES];
    }];
    
    //药品明细按钮
    UIBarButtonItem *rightBarButtonItem = [[UIBarButtonItem alloc] initWithTitle:@"药品明细" style:UIBarButtonItemStylePlain target:self action:@selector(clickDrugDetailButtonEvent:)];
    self.navigationItem.rightBarButtonItem = rightBarButtonItem;
}

//请求数据
- (void)requestData {
    if (!self.orderId.length) {
        
        [self.view makeToast:@"数据出错，orderId为空" duration:2 position:CSToastPositionCenter];
        return;
    }
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000228",@"method_code",self.orderId,@"orderId", nil];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        NSLog(@"用药详情 responseobject info = %@",responseObject);
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                if (![mySelf dataIsNull:[dataDic objectForKey:@"orderStatus"]]) {
                    if ([[dataDic objectForKey:@"orderStatus"] isEqualToString:@"CANCELED"]) {
                        
                        hud.hidden = YES;
                        [mySelf createUI:YES];
                        [self.view makeToast:@"订单已失效" duration:2 position:CSToastPositionCenter];
                        return;
                    }
                }
                _backColor = [responseObject objectForKey:@"orderStatus"];
                if ([[responseObject objectForKey:@"orderStatus"] isEqualToString:@"WAIT_FOR_PAY"]) {
                    
                    self.tableView.backgroundColor = [UIColor whiteColor];
                } else {
                    self.tableView.backgroundColor = [UIColor br_backgroundColor];
                }
                
                mySelf.dataDic = dataDic;
                
                _isSecurity = [responseObject objectForKey:@"isSecrecy"];
                //用药详情
                if ([responseObject objectForKey:@"details"] && [[responseObject objectForKey:@"details"] count] > 0) {
                    _detailsArray = [NSArray arrayWithArray:[responseObject objectForKey:@"details"]];
                }
                
                if (![mySelf dataIsNull:[dataDic objectForKey:@"payStatus"]]) {
                    if ([[dataDic objectForKey:@"payStatus"] isEqualToString:@"NO_PAY"]) {
                        //未支付
                         mySelf.btnShowOrNot = NSBtnShow;
                        
                    }
                }
                
                if (![mySelf dataIsNull:[dataDic objectForKey:@"orderStatus"]]) {
                    
                    if ([[dataDic objectForKey:@"orderStatus"] isEqualToString:@"EXPIRE"]) {
                        //已过期
                        mySelf.btnShowOrNot = NSBtnNotShow;
                        
                    }
                    
                    
                    OrderStatus orderStatus = [mySelf getOrderStatusWithOrderStatusStr:[dataDic objectForKey:@"orderStatus"]];
                    NSString *prescriptionUrl = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"prescriptionUrl"]];
                    if (orderStatus == OrderStatusWaitForSend || prescriptionUrl.length) {

                        //如果订单状态是未发送，那么设置成拍照开方未发送类型
                        //处方图片的地址不为空也为拍照开方状态
                        mySelf.prescriptionType = BRPrescriptionTypePhoto;

                    }
                    
                }
                
                [mySelf createUI:NO];
                
            } else {
                
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        
        [mySelf createNoWIFIView];
        
        hud.hidden = YES;
    }];
}

//无网络视图
- (void)createNoWIFIView {
    self.noDataView = [[BRNoDataView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, self.view.frame.size.height) withImage:[UIImage imageNamed:@"no_WIFI"] withText:@"网络异常，请手动点击重新加载"];
    [self.view addSubview:self.noDataView];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(requestData)];
    [self.noDataView addGestureRecognizer:tap];
}

- (void)createUI:(BOOL)isInvalid {
    
    if (self.dataDic) {
        
        NSString *nameStr = @"姓名: ";
        if (![self dataIsNull:[self.dataDic objectForKey:@"name"]]) {
            nameStr = [nameStr stringByAppendingString:[NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"name"]]];
        }
        NSString *ageStr = @"年龄: ";
        if (![self dataIsNull:[self.dataDic objectForKey:@"age"]]) {
            ageStr = [ageStr stringByAppendingString:[NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"age"]]];
        }
        NSString *sexStr = @"性别: ";
        if (![self dataIsNull:[self.dataDic objectForKey:@"sex"]]) {
            NSString *sexValue = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"sex"]];
            if ([sexValue isEqualToString:@"1"]) {
                sexStr = [sexStr stringByAppendingString:@"男"];
            } else {
                sexStr = [sexStr stringByAppendingString:@"女"];
            }
        }
        NSString *preg = @"";
        if (![self dataIsNull:[self.dataDic objectForKey:@"ispregnant"]]) {
            NSString *pregnantValue = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"ispregnant"]];
            if ([pregnantValue isEqualToString:@"1"]) {
                preg = @"怀孕";
            }
        }
        [self.viewHeader removeFromSuperview];
        self.viewHeader = nil;
        // 如果是用药详情模式且有订单ID，增加高度以容纳订单编号
        CGFloat headerHeight = FONTSIZE(45);
        if (self.medicatedInfoOrPreview == NSMedicatedInfo && ![self dataIsNull:[self.dataDic objectForKey:@"id"]]) {
            headerHeight += 30; // 为订单编号增加30的高度
        }
        self.viewHeader = [self createHeaderView:CGRectMake(0, 0, self.view.frame.size.width, headerHeight) text:nameStr age:ageStr sex:sexStr pregnancy:preg];
        [self.view addSubview:self.viewHeader];
        
        _bianzhengStr = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"dialectical"]];
        _anyuStr = @"";
        if (![[self.dataDic objectForKey:@"comment"] isKindOfClass:[NSNull class]]) {
            _anyuStr = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"comment"]];
        }
        
        /*
         * 有按语且是拍照开方类型 显示4个
         * 有按语不是拍照开方类型 显示3个
         * 无按语是拍照开方类型   显示3个
         */
        //NSString *prescriptionUrl = [self.dataDic objectForKey:@"prescriptionUrl"];
        if (_prescriptionType == BRPrescriptionTypePhoto && !_anyuStr.length) {
            
            //有按语不是拍照开方类型
            _titleTextArr = @[@"辨证",@"用药详情",@"原拍方照片"];
        }
        else  {
            
            _titleTextArr = @[@"辨证",@"用药详情",@"按语",@"原拍方照片"];
        }
        
        _medicatedArr = [NSMutableArray array];
        if (![self dataIsNull:[self.dataDic objectForKey:@"details"]]) {
            
            NSString *isSecrecy = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"isSecrecy"]];
            if ([isSecrecy isEqual:[NSNull null]]) {
                isSecrecy = @"0";
            }
            
            NSArray *detailsArr = [self.dataDic objectForKey:@"details"];
            for (NSDictionary *dic in detailsArr) {
                NSString *dataStr = @"";
               
                
                //不保密
//                if ([isSecrecy isEqualToString:@"0"]) {
                    if (![self dataIsNull:[dic objectForKey:@"name"]]) {
                        dataStr = [dataStr stringByAppendingString:[NSString stringWithFormat:@"%@", [dic objectForKey:@"name"]]];
                    }
                    
                    if (![self dataIsNull:[dic objectForKey:@"amount"]]) {
                        dataStr = [dataStr stringByAppendingString:[NSString stringWithFormat:@"%@%@",[dic objectForKey:@"amount"],[dic objectForKey:@"unit"]]];
                    }
                    
                    if (![self dataIsNull:[dic objectForKey:@"type"]]) {
                        NSString *typeStr = [NSString stringWithFormat:@"%@", [dic objectForKey:@"type"]];
                        if (typeStr.length) {
                            dataStr = [dataStr stringByAppendingString:[NSString stringWithFormat:@"(%@)",typeStr]];
                        }
                    }
//                }
//                //全方保密
//                if ([isSecrecy isEqualToString:@"1"]) {
//
//                }
//                //剂量保密
//                if ([isSecrecy isEqualToString:@"2"]) {
//                    if (![self dataIsNull:[dic objectForKey:@"name"]]) {
//                        dataStr = [dataStr stringByAppendingString:[dic objectForKey:@"name"]];
//                    }
//                }
                
                [_medicatedArr addObject:[dataStr copy]];
            }
            
        }
        
        [_medicatedView removeFromSuperview];
        [_medicatedInfoView removeFromSuperview];
        _medicatedInfoView = nil;
        _medicatedView = nil;
        
        _medicatedView = [self medicatedView:CGRectMake(0, FONTSIZE(45)+FONTSIZE(15), self.view.frame.size.width, 0) medicatedArr:_medicatedArr];
        _medicatedInfoView = [self medicatedInfoView:CGRectMake(0, _medicatedView.frame.origin.y+_medicatedView.frame.size.height, _medicatedView.frame.size.width, 0) medicatedArr:self.dataDic];
        _bianzhengSize = [_bianzhengStr boundingRectWithSize:CGSizeMake(self.view.frame.size.width-FONTSIZE(15)*2, 1300) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:kFontLight(16)} context:nil].size;
        _anyuSize = [_anyuStr boundingRectWithSize:CGSizeMake(self.view.frame.size.width-FONTSIZE(15)*2, 1300) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:kFontLight(16)} context:nil].size;
        
    }
    
    CGRect tableViewFrame = CGRectZero;
    
    if (self.btnShowOrNot == NSBtnShow) {
        
        if ([[self.dataDic objectForKey:@"orderStatus"] isEqualToString:@"WAIT_FOR_SEND"]) {
            
            //待发送
            [self bottomCancelAndSendView];
            
        }
        else {
            
            NSString *drugForm = [NSString stringWithFormat:@"%@", [self.dataDic objectForKey:@"drugForm"]];
            
            if ([drugForm isEqualToString:@"膏方"] || [drugForm isEqualToString:@"水丸"] || [drugForm isEqualToString:@"蜜丸"] || [drugForm isEqualToString:@"散剂"] || [drugForm isEqualToString:@"胶囊"]) {
                //作废与复制
                [self downEditAndCopyPrescriptionView];
            }
            else {
                //作废、修改副数与复制
                [self downView];
            }
            
        }
        
        tableViewFrame = CGRectMake(0, self.dataDic?self.viewHeader.frame.size.height:0, self.view.frame.size.width, self.view.frame.size.height-self.viewHeader.frame.size.height - 57 - kTabbarSafeBottomMargin);
        
    }
    else {
        
        //用药预览
        if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
            //发送给患者
            [self downPreviewView];
            
            tableViewFrame = CGRectMake(0, self.dataDic?self.viewHeader.frame.size.height:0, self.view.frame.size.width, self.view.frame.size.height-self.viewHeader.frame.size.height-FONTSIZE(45)-self.navigationController.navigationBar.frame.origin.y-self.navigationController.navigationBar.frame.size.height - kTabbarSafeBottomMargin);
        }
        //已支付的
        else {
            
            if (!isInvalid) {
                //只是复制
                [self downCopyPrescriptionView];
            }
            
            
            tableViewFrame = CGRectMake(0, self.dataDic?self.viewHeader.frame.size.height:0, self.view.frame.size.width, self.view.frame.size.height-self.viewHeader.frame.size.height - 57 - kTabbarSafeBottomMargin);
        }
    }
    
    if (!self.tableView) {
        self.tableView = [[UITableView alloc] initWithFrame:tableViewFrame];
        self.tableView.delegate = self;
        self.tableView.dataSource = self;
        self.tableView.showsVerticalScrollIndicator = NO;
        self.tableView.emptyDataSetSource = self;
        self.tableView.emptyDataSetDelegate = self;
        self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
        self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        if ([_backColor isEqualToString:@"WAIT_FOR_PAY"]) {
            self.tableView.backgroundColor = [UIColor whiteColor];
        } else {
            self.tableView.backgroundColor = [UIColor br_backgroundColor];
        }
        [self.view addSubview:self.tableView];
        if (@available(iOS 11.0, *)) {
            self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            self.automaticallyAdjustsScrollViewInsets = NO;
        }
    } else {
        [self.tableView reloadData];
    }
    
    //下载原开方照片
    if (_prescriptionType == BRPrescriptionTypePhoto) {
        
        [self downloadImgWithImgUrl:[_dataDic objectForKey:@"prescriptionUrl"]];
//        [self downloadImgWithImgUrl:@"http://www.cocoachina.com/cms/uploads/allimg/140621/8369_140621165159_1.jpg"];
        
    }
    
}

//弹出视图
- (void)createAlert:(NSString *)title {
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    
    __weak typeof(alertView) aView = alertView;
    [alertView showAlertView:title completion:^{
        [aView close];
    }];
}

#pragma mark - 药品明细
- (void)clickDrugDetailButtonEvent:(UIBarButtonItem *)sender {
    
    DrugDetailListViewController *drugDetailListVC = [[DrugDetailListViewController alloc] init];
    drugDetailListVC.isPreview = (self.medicatedInfoOrPreview == NSMedicatedPreview ? YES : NO);
    drugDetailListVC.preViewListArray = [NSArray arrayWithArray:[self.dataDic objectForKey:@"details"]];
    [self.navigationController pushViewController:drugDetailListVC animated:YES];
}

#pragma mark -UITableView 代理
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    if (self.dataDic) {
        if (self.btnShowOrNot == NSBtnNotShow) {
            if (self.medicatedInfoOrPreview == NSMedicatedInfo) {
                
                /*
                 用药详情或者叫订单详情
                 按语不为空显示 按语部分
                 拍方订单显示拍方照片
                 */
                //拍方照片的URL
                /*
                NSString *prescriptionUrl = [self.dataDic objectForKey:@"prescriptionUrl"];
                
                if (_anyuStr.length && prescriptionUrl.length) {
                    return 4;
                }
                else if (_anyuStr.length || prescriptionUrl.length){
                    return 3;
                }
                else {
                    return 2;
                }
                 */
                
                if (_anyuStr.length && _prescriptionType == BRPrescriptionTypePhoto) {
                    return 4;
                }
                else if (_anyuStr.length || _prescriptionType == BRPrescriptionTypePhoto){
                    return 3;
                }
                else {
                    return 2;
                }
                
            }
            else{
                return 2;
            }
        }
        else {
            
            if (_prescriptionType == BRPrescriptionTypePhoto) {
                return 3;
            }
            else {
                return 2;
            }
            
        }
        
    }
    else {
        return 0;
    }
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 1) {
        return _medicatedView.frame.size.height + FONTSIZE(45) + _medicatedInfoView.frame.size.height;
    } else if (indexPath.section == 2) {
        
        if (_anyuStr.length != 0) {
            return FONTSIZE(45) + FONTSIZE(15) + _anyuSize.height + FONTSIZE(20);
        }
        else{
            
            /*
            if (_presPhoto) {
                
                CGSize photoSize = _presPhoto.size;
                CGFloat photoWidth = photoSize.width;
                CGFloat photoHeight = photoSize.height;
                
                CGFloat view_width = self.view.frame.size.width - 30;
                
                //横着拍的
                CGFloat scale = photoWidth / photoHeight;
                
                CGFloat newPhotoWidth = view_width;
                CGFloat newPhotoHeight = view_width / scale;
                
                return 45 + 15 + newPhotoHeight + 17;
                
            }
            
            //显示原拍方照片
            return 45 + 15 + 100 + 17;
             */
            return 45 + 15 + 368/2 + 17;
            
        }
        
    } else if (indexPath.section == 3){
        /*
        if (_presPhoto) {
            
            CGSize photoSize = _presPhoto.size;
            CGFloat photoWidth = photoSize.width;
            CGFloat photoHeight = photoSize.height;
            
            CGFloat view_width = self.view.frame.size.width - 30;
            
            //横着拍的
            CGFloat scale = photoWidth / photoHeight;
            
            CGFloat newPhotoWidth = view_width;
            CGFloat newPhotoHeight = view_width / scale;
            
            return 45 + 15 + newPhotoHeight + 17;
            
        }
        
        //显示原拍方照片
        return 45 + 15 + 100 + 17;
         */
        return 45 + 15 + 368/2 + 17;
        
    } else {
        return FONTSIZE(45) + FONTSIZE(15) + _bianzhengSize.height + FONTSIZE(20);
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return FONTSIZE(10);
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor br_backgroundColor];
    return view;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat sectionHeaderHeight = FONTSIZE(10);
    if (scrollView.contentOffset.y <= sectionHeaderHeight && scrollView.contentOffset.y >= 0) {
        scrollView.contentInset = UIEdgeInsetsMake(-scrollView.contentOffset.y, 0, 0, 0);
    } else if (scrollView.contentOffset.y>=sectionHeaderHeight) {
        scrollView.contentInset = UIEdgeInsetsMake(-sectionHeaderHeight, 0, 0, 0);
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellID = @"cellId";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellID];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.clipsToBounds = YES;
        cell.backgroundColor = [UIColor whiteColor];
        
    }
    
    for (UIView *view in cell.subviews) {
        if (view.tag == 50 || view.tag == 52 || view.tag == 53) {
            [view removeFromSuperview];
        }
    }
    
    for (UILabel *label in cell.subviews) {
        if (label.tag == 51 || label.tag == 52) {
            [label removeFromSuperview];
        }
    }
    
    for (UIImageView *imgView in cell.contentView.subviews) {
        if (imgView.tag == 54) {
            [imgView removeFromSuperview];
        }
    }
    
    UIView *topView = [self cellTitleView:CGRectMake(0, 0, self.tableView.frame.size.width, FONTSIZE(45)) text:_titleTextArr[indexPath.section]];
    topView.tag = 50;
    [cell addSubview:topView];
    
    switch (indexPath.section) {
        case 0:
        {
            UILabel *label = [self createLabel:CGRectMake(FONTSIZE(15), topView.frame.size.height+FONTSIZE(15), self.tableView.frame.size.width-FONTSIZE(15)*2, _bianzhengSize.height) text:_bianzhengStr];
            label.numberOfLines = 0;
            label.tag = 51;
            [cell addSubview:label];
        }
            break;
        case 1:
        {
            
            _medicatedView.tag = 52;
            [cell addSubview:_medicatedView];
            
            _medicatedInfoView.tag = 53;
            [cell addSubview:_medicatedInfoView];
        }
            break;
        case 2:
        {
            
            if (_anyuStr.length != 0) {
                
                //显示按语
                UILabel *label = [self createLabel:CGRectMake(FONTSIZE(15), topView.frame.size.height+FONTSIZE(15), self.tableView.frame.size.width-FONTSIZE(15)*2, _anyuSize.height) text:_anyuStr];
                label.numberOfLines = 0;
                label.tag = 52;
                [cell addSubview:label];
                
            }
            else {
                
                BRCanTapImgView *imgView = [[BRCanTapImgView alloc]init];
                if (_presPhoto) {
                    imgView.image = _presPhoto;
                }
                else {
                    [self downloadImgWithImgUrl:[self.dataDic objectForKey:@"prescriptionUrl"]];
//                    [self downloadImgWithImgUrl:@"http://www.cocoachina.com/cms/uploads/allimg/140621/8369_140621165159_1.jpg"];
                }
                imgView.tag = 54;
                imgView.contentMode = UIViewContentModeScaleAspectFill;
                imgView.clipsToBounds = YES;
                [cell.contentView addSubview:imgView];
                
                if (_presPhoto) {
                    
                    CGSize photoSize = _presPhoto.size;
                    CGFloat photoWidth = photoSize.width;
                    CGFloat photoHeight = photoSize.height;
                    
                    if (photoWidth <= photoHeight) {
                        imgView.frame = CGRectMake((WIDTH(self.view)-kHorizontalMargin*2-250/2)/2+kHorizontalMargin, kHorizontalMargin+45, 250/2, 368/2);
                    }
                    else {
                        imgView.frame = CGRectMake(kHorizontalMargin, kHorizontalMargin+45, WIDTH(self.view)-kHorizontalMargin*2, 368/2);
                    }
                    
                }
                else {
                    imgView.frame = CGRectMake(kHorizontalMargin, kHorizontalMargin+45, WIDTH(self.view)-kHorizontalMargin*2, 368/2);
                }
                
                [[imgView.tap rac_gestureSignal]subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
                    [self getPhotoBrowser];
                }];
                
            }
            
        }
            break;
        
        case 3:
        {
            
            BRCanTapImgView *imgView = [[BRCanTapImgView alloc]initWithFrame:CGRectMake(15, 45+15, WIDTH(self.view) - 30, 100)];
            if (_presPhoto) {
                imgView.image = _presPhoto;
            }
            else {
                [self downloadImgWithImgUrl:[self.dataDic objectForKey:@"prescriptionUrl"]];
//                [self downloadImgWithImgUrl:@"http://www.cocoachina.com/cms/uploads/allimg/140621/8369_140621165159_1.jpg"];
            }
            imgView.tag = 54;
            [cell.contentView addSubview:imgView];
            
            if (_presPhoto) {
                
                CGSize photoSize = _presPhoto.size;
                CGFloat photoWidth = photoSize.width;
                CGFloat photoHeight = photoSize.height;
                
                if (photoWidth <= photoHeight) {
                    imgView.frame = CGRectMake((WIDTH(self.view)-kHorizontalMargin*2-250/2)/2+kHorizontalMargin, kHorizontalMargin+45, 250/2, 368/2);
                }
                else {
                    imgView.frame = CGRectMake(kHorizontalMargin, kHorizontalMargin+45, WIDTH(self.view)-kHorizontalMargin*2, 368/2);
                }
                
            }
            else {
                imgView.frame = CGRectMake(kHorizontalMargin, kHorizontalMargin+45, WIDTH(self.view)-kHorizontalMargin*2, 368/2);
            }
            
            [[imgView.tap rac_gestureSignal]subscribeNext:^(__kindof UIGestureRecognizer * _Nullable x) {
                [self getPhotoBrowser];
            }];
            
        }
            break;
            
    }
    
    return cell;
}

#pragma mark - 创建姓名，年龄，性别视图
- (UIView *)createHeaderView:(CGRect)frame text:(NSString *)nameStr age:(NSString *)ageStr sex:(NSString *)sexStr pregnancy:(NSString *)pregnancyStr {
    UIView *view = [[UIView alloc] initWithFrame:frame];
    view.backgroundColor = [UIColor whiteColor];
    
    // 订单编号显示（仅在用药详情模式下显示）
    CGFloat orderIdHeight = 0;
    if (self.medicatedInfoOrPreview == NSMedicatedInfo && self.dataDic && ![self dataIsNull:[self.dataDic objectForKey:@"id"]]) {
        orderIdHeight = 30;
        NSString *orderIdStr = [NSString stringWithFormat:@"订单编号: %@", [self.dataDic objectForKey:@"id"]];
        
        // 计算复制按钮的宽度
        CGFloat copyButtonWidth = 50;
        CGFloat labelWidth = view.frame.size.width - FONTSIZE(30) - copyButtonWidth - 10; // 10为间距
        
        UILabel *orderIdLabel = [[UILabel alloc] initWithFrame:CGRectMake(FONTSIZE(15), 5, labelWidth, orderIdHeight)];
        orderIdLabel.textColor = [UIColor blackColor];
        orderIdLabel.font = kFontLight(14);
        orderIdLabel.text = orderIdStr;
        [view addSubview:orderIdLabel];
        
        // 复制按钮
        CGFloat iconButtonSize = 24;
        UIButton *copyButton = [[UIButton alloc] initWithFrame:CGRectMake(orderIdLabel.frame.origin.x + orderIdLabel.frame.size.width + 10, 5 + (orderIdHeight - iconButtonSize)/2, iconButtonSize, iconButtonSize)];
        [copyButton setImage:[UIImage imageNamed:@"document.on.document"] forState:UIControlStateNormal];
        copyButton.backgroundColor = [UIColor clearColor];
        [copyButton addTarget:self action:@selector(copyOrderIdAction:) forControlEvents:UIControlEventTouchUpInside];
        [view addSubview:copyButton];
    }
    
    CGSize size = CGSizeZero;
    CGSize nameSize = [nameStr boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
    CGSize ageSize = [ageStr boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
    CGSize sexSize = [sexStr boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
    
    if (pregnancyStr.length) {
        size = [pregnancyStr boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(14)} context:nil].size;
        
        UILabel *preLabel = [[UILabel alloc] initWithFrame:CGRectMake(view.frame.size.width-FONTSIZE(15)-size.width, orderIdHeight + 5 + (view.frame.size.height-orderIdHeight)/2+nameSize.height/2-size.height, size.width, size.height)];
        preLabel.textColor = [UIColor colorWithRed:255.0/255 green:133.0/255 blue:133.0/255 alpha:1.f];
        preLabel.font = FONT_Light(14);
        preLabel.text = pregnancyStr;
        [view addSubview:preLabel];
    }
    
    float uiAllW = uiAllW = nameSize.width + ageSize.width + sexSize.width;
    float viewAllW = 0;
    
    if (pregnancyStr.length) {
        viewAllW = view.frame.size.width - FONTSIZE(15)*2 - size.width - FONTSIZE(10);
        
    } else {
        viewAllW = view.frame.size.width - FONTSIZE(15)*2;
        
    }
    
    float jianju = 0;
    UILabel *nameLabel = nil;
    //预留最低间距 FONTSIZE(10)
    if ((uiAllW + FONTSIZE(10)*2) < viewAllW) {
        
        jianju = (viewAllW - uiAllW)/2.0;
        
        nameLabel = [self createLabel:CGRectMake(FONTSIZE(15), orderIdHeight + 5, nameSize.width, view.frame.size.height - orderIdHeight - 5) text:nameStr];
        
    } else {
        float nameLabelMaxW = 0;
        if (pregnancyStr.length) {
            nameLabelMaxW = view.frame.size.width - FONTSIZE(15)*2 - size.width - FONTSIZE(10) - sexSize.width - ageSize.width - FONTSIZE(10)*2;
            
        } else {
            nameLabelMaxW = view.frame.size.width - FONTSIZE(15)*2 - sexSize.width - ageSize.width - FONTSIZE(10)*2;
        }
        
        jianju = FONTSIZE(10);
        nameLabel = [self createLabel:CGRectMake(FONTSIZE(15), orderIdHeight + 5, nameLabelMaxW, view.frame.size.height - orderIdHeight - 5) text:nameStr];
    }
    
    [view addSubview:nameLabel];
    
    UILabel *ageLabel = [self createLabel:CGRectMake(nameLabel.frame.origin.x+nameLabel.frame.size.width+jianju, orderIdHeight + 5, ageSize.width, view.frame.size.height - orderIdHeight - 5) text:ageStr];
    [view addSubview:ageLabel];
    
    UILabel *sexLabel = [self createLabel:CGRectMake(ageLabel.frame.origin.x+ageLabel.frame.size.width+jianju, orderIdHeight + 5, sexSize.width, view.frame.size.height - orderIdHeight - 5) text:sexStr];
    [view addSubview:sexLabel];
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, view.frame.size.width, 1)];
    lineView.backgroundColor = [UIColor colorWithRed:234.0/255 green:234.0/255 blue:234.0/255 alpha:1.f];
    [view addSubview:lineView];
    
    UIView *lineView2 = [[UIView alloc] initWithFrame:CGRectMake(0, view.frame.size.height-1, view.frame.size.width, 1)];
    lineView2.backgroundColor = [UIColor colorWithRed:234.0/255 green:234.0/255 blue:234.0/255 alpha:1.f];
    [view addSubview:lineView2];
    
    return view;
}

- (UILabel *)createLabel:(CGRect)frame text:(NSString *)text {
    UILabel *label = [[UILabel alloc] initWithFrame:frame];
    label.textColor = [UIColor colorWithRed:22.0/255 green:25.0/255 blue:30.0/255 alpha:1.f];
//    label.font = FONT_Light(16);
    label.font = kFontLight(16);
    label.text = text;
    
    return label;
}

- (UILabel *)createTitleLabel:(CGRect)frame text:(NSString *)text {
    UILabel *label = [[UILabel alloc] initWithFrame:frame];
    label.textColor = [UIColor colorWithRed:22.0/255 green:25.0/255 blue:30.0/255 alpha:1.f];
    label.font = FONT_Regular(16);
    label.textAlignment = NSTextAlignmentCenter;
    label.text = text;
    
    return label;
}

#pragma mark - cell顶部视图
- (UIView *)cellTitleView:(CGRect)frame text:(NSString *)text {
    UIView *view = [[UIView alloc] initWithFrame:frame];
    view.backgroundColor = [UIColor whiteColor];
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, view.frame.size.height-1, view.frame.size.width, 1)];
    lineView.backgroundColor = [UIColor colorWithRed:246.0/255 green:246.0/255 blue:246.0/255 alpha:1.f];
    [view addSubview:lineView];
    
    CGSize size = [text boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:[UIFont systemFontOfSize:FONTSIZE(16)]} context:nil].size;
    UILabel *label = [self createTitleLabel:CGRectMake(view.frame.size.width/2-(size.width + 10)/2 + 5, 0, size.width + 10, view.frame.size.height) text:text];
    [view addSubview:label];
    
    UIImageView *leftImageV = [[UIImageView alloc] initWithFrame:CGRectMake(label.frame.origin.x-FONTSIZE(20), view.frame.size.height/2-FONTSIZE(20)/2, FONTSIZE(20), FONTSIZE(20))];
    leftImageV.image = [UIImage imageNamed:@"patient_point"];
    [view addSubview:leftImageV];
    
    UIImageView *rightImageV = [[UIImageView alloc] initWithFrame:CGRectMake(label.frame.origin.x+label.frame.size.width, view.frame.size.height/2-FONTSIZE(20)/2, FONTSIZE(20), FONTSIZE(20))];
    rightImageV.image = [UIImage imageNamed:@"patient_point"];
    [view addSubview:rightImageV];
    
    
    return view;
}

#pragma mark - 用药详情中药展示
- (UIView *)medicatedView:(CGRect)frame medicatedArr:(NSArray *)arr {
    UIView *view = [[UIView alloc] initWithFrame:frame];
    
    float lineNum = 1;
    float currX = FONTSIZE(15);
    float labelH = 0;
    for (int i = 0; i < arr.count; i++) {
        NSString *mediName = arr[i];
        NSMutableAttributedString *attStr = [[NSMutableAttributedString alloc] initWithString:mediName];
        
        CGSize size = [mediName boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
        if ([[self.dataDic objectForKey:@"drugForm"] isEqualToString:@"饮片"] || [[self.dataDic objectForKey:@"drugForm"] isEqualToString:@"代煎"]) {
            if ([mediName rangeOfString:@"("].location != NSNotFound) {
                if ([mediName rangeOfString:@")"].location != NSNotFound) {
                    if ([mediName rangeOfString:@")"].location > [mediName rangeOfString:@"("].location) {
                        NSString *str1 = [mediName substringWithRange:NSMakeRange(0, [mediName rangeOfString:@"("].location)];
                        NSString *str2 = [mediName substringFromIndex:[mediName rangeOfString:@"("].location];
                        CGSize size2 = [str1 boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
                        CGSize size3 = [str2 boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(12)} context:nil].size;
                        size = CGSizeMake(size2.width+size3.width+1, size.height);
                        
                        [attStr addAttribute:NSFontAttributeName value:FONT_Light(16) range:NSMakeRange(0, [mediName rangeOfString:@"("].location)];
                        [attStr addAttribute:NSFontAttributeName value:FONT_Light(12) range:NSMakeRange([mediName rangeOfString:@"("].location, mediName.length-[mediName rangeOfString:@"("].location)];
                        [attStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:22.0/255 green:25.0/255 blue:30.0/255 alpha:1.f] range:NSMakeRange(0, [mediName rangeOfString:@"("].location)];
                        [attStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:239.0/255 green:77.0/255 blue:59.0/255 alpha:1.f] range:NSMakeRange([mediName rangeOfString:@"("].location, mediName.length-[mediName rangeOfString:@"("].location)];
                    }
                }
            }
        }
        
        labelH = size.height;
        UILabel *label = nil;
        if (size.width <= (view.frame.size.width-FONTSIZE(15)*2)) {
            
            if ((currX+size.width) <= (view.frame.size.width-FONTSIZE(15))) {
                
                label = [self createLabel:CGRectMake(currX, (size.height+FONTSIZE(15))*(lineNum-1), size.width, size.height) text:mediName];
                currX += (size.width+FONTSIZE(25));
                
            } else {
                
                currX = FONTSIZE(15);
                lineNum ++;
                label = [self createLabel:CGRectMake(currX, (size.height+FONTSIZE(15))*(lineNum-1), size.width, size.height) text:mediName];
                currX += (size.width+FONTSIZE(25));
            }
            
        } else {
            
            if (i) {
                lineNum ++;
            }
            
            currX = FONTSIZE(15);
            label = [self createLabel:CGRectMake(currX, (size.height+FONTSIZE(15))*(lineNum-1), view.frame.size.width-FONTSIZE(15)*2, size.height) text:nil];
            currX = view.frame.size.width;
        }
        label.attributedText = attStr;
        [view addSubview:label];
    }
    
    view.frame = CGRectMake(frame.origin.x, frame.origin.y, frame.size.width, lineNum*(labelH+FONTSIZE(15)));
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, view.frame.size.height-1, view.frame.size.width, 1)];
    lineView.backgroundColor = [UIColor colorWithRed:246.0/255 green:246.0/255 blue:246.0/255 alpha:1.f];
    [view addSubview:lineView];
    
    return view;
}

#pragma mark - 用药详情中药说明
- (UIView *)medicatedInfoView:(CGRect)frame medicatedArr:(NSDictionary *)dic {
    UIView *view = [[UIView alloc] initWithFrame:frame];
    
    NSLog(@"dic === %@",dic);
    
    //是否显示内服
    NSMutableArray *titleArr = [NSMutableArray arrayWithArray:@[@"药方剂型"]];
    
    // 检查规格字段是否有数据，有则添加规格显示
    NSString *packageSpec = @"";
    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
        // 预览状态从 orderDict 获取
        packageSpec = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"packageSpec"]];
    } else {
        // 详情状态从 dataDic 获取
        packageSpec = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"packageSpec"]];
    }
    if (![self dataIsNull:packageSpec] && packageSpec.length > 0) {
        [titleArr addObject:@"规格"];
    }
    
    // 检查辅料字段是否有数据，有则添加辅料显示
    NSString *makeMaterial = @"";
    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
        // 预览状态从 orderDict 获取
        makeMaterial = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"makeMaterial"]];
    } else {
        // 详情状态从 dataDic 获取
        makeMaterial = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"makeMaterial"]];
    }
    // 将 "(null)"、"<null>" 以及空白处理为无数据
    if ([makeMaterial isEqualToString:@"(null)"] || [makeMaterial isEqualToString:@"<null>"]) {
        makeMaterial = @"";
    }
    makeMaterial = [makeMaterial stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (makeMaterial.length > 0) {
        [titleArr addObject:@"辅料"];
    }
    
    [titleArr addObject:@"用法用量"];
    
    NSString *useMethod = [NSString stringWithFormat:@"%@", [dic objectForKey:@"mode"]];
    if (useMethod.length > 0 &&
        ([[dic objectForKey:@"drugForm"] isEqualToString:@"饮片"] || [[dic objectForKey:@"drugForm"] isEqualToString:@"代煎"] || [[dic objectForKey:@"drugForm"] isEqualToString:@"散剂"]) && ([useMethod isEqualToString:@"内服"] || [useMethod isEqualToString:@"外用"])) {
        [titleArr addObject:@"用药方法"];
    }
    
    [titleArr addObjectsFromArray:@[@"服药禁忌",@"补充说明",@"保密类型"]];
    
    NSMutableArray *arr = [NSMutableArray arrayWithArray:titleArr];
    
    NSString *orderStatusStr = [NSString stringWithFormat:@"%@", [dic objectForKey:@"orderStatus"]];
    OrderStatus orderStatus = [self getOrderStatusWithOrderStatusStr:orderStatusStr];
    if (orderStatus > OrderStatusWaitForPay && orderStatus < OrderStatusCanceled) {
        
        //显示药房代码
        [arr insertObject:@"药房代码" atIndex:0];
        
    }
    
    //若为饮片，显示是否可以自煎或者代煎
    NSString *drugForm = [NSString stringWithFormat:@"%@", [dic objectForKey:@"drugForm"]];
    
    if ([drugForm containsString:@"代煎"]) {
        [arr addObject:@"是否代煎"];
        [arr addObject:@"代煎偏好"];
    }
    
    
    if (self.btnShowOrNot == NSBtnNotShow && self.medicatedInfoOrPreview == NSMedicatedPreview) {
        
    }
    else {
        [arr addObject:@"订单金额"];
    }
    
    CGSize size = [@"药方剂型" boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
    float heightY = FONTSIZE(24);
    for (int i = 0; i < arr.count; i++) {
        
        CGSize size2 = CGSizeZero;
        NSString *infoStr = @"";
        
        // 动态计算用法用量的位置索引
        NSInteger usageIndex = [arr indexOfObject:@"用法用量"];
        if (usageIndex == NSNotFound) {
            usageIndex = 1; // 默认位置，防止找不到时的异常
        }
//        else {
//            infoStr = [self getInfoStrWithDict:dic index:i title:[arr objectAtIndex:i] isShowDCID:NO];
//        }
        
        infoStr = [self getInfoStrWithDict:dic index:i title:[arr objectAtIndex:i] isShowDCID:YES];
        
        
        size2 = [infoStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18), 1300) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(FONTSIZE(15), heightY, size.width, size.height)];
        titleLabel.font = FONT_Light(16);
        titleLabel.textColor = [UIColor colorWithRed:177.0/255 green:177.0/255 blue:177.0/255 alpha:1.f];
        titleLabel.text = arr[i];
        [view addSubview:titleLabel];
        
        heightY = heightY + size2.height + FONTSIZE(15);
        
        UILabel *infoLabel;
        if (i == usageIndex) {
            
            NSString *allStr = @"";
            NSUInteger allStart = 1;
            if (![self dataIsNull:[dic objectForKey:@"totalCount"]]) {
                allStr = [NSString stringWithFormat:@"共 %@ 剂",[dic objectForKey:@"totalCount"]];
            }
            if (![self dataIsNull:[dic objectForKey:@"drugForm"]]) {
                if (![[dic objectForKey:@"drugForm"] isEqualToString:@"颗粒"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"饮片"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"代煎"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"外用中药"]) {
                    NSString *mrjc = @"";
                    if (![self dataIsNull:[dic objectForKey:@"mrjc"]]) {
                        mrjc = [NSString stringWithFormat:@"%@", [dic objectForKey:@"mrjc"]];
                    }
                    allStr = [NSString stringWithFormat:@"每日 %@ 次",mrjc];
                    allStart = 2;
                    
                    if (self.refreshBtn) {
                        
                        [self.refreshBtn removeFromSuperview];
                        self.refreshBtn = nil;
                        self.removeBtn.frame = CGRectMake(self.view.frame.size.width-FONTSIZE(15)-FONTSIZE(80), self.view.frame.size.height-(FONTSIZE(61)-FONTSIZE(38))/2-FONTSIZE(38), FONTSIZE(80), FONTSIZE(38));
                        
                    }
                }
            }
            NSMutableAttributedString *allAttStr = [[NSMutableAttributedString alloc] initWithString:allStr];
            [allAttStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f] range:NSMakeRange(allStart, allStr.length-allStart-1)];
            CGSize allSize = [allStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            UILabel *allLabel = [self createLabel:CGRectMake(titleLabel.frame.origin.x+titleLabel.frame.size.width+FONTSIZE(18), titleLabel.frame.origin.y, allSize.width, allSize.height) text:nil];
            allLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
            allLabel.attributedText = allAttStr;
            [view addSubview:allLabel];

            NSString *dayStr = @"";
            
            //胶囊颗粒数 如： (5颗)
            NSString *capsuleNumStr = @"";
            
            if (![self dataIsNull:[dic objectForKey:@"preUsage"]]) {
                dayStr = [NSString stringWithFormat:@"每日 %@ 剂",[dic objectForKey:@"preUsage"]];
            }
            if (![self dataIsNull:[dic objectForKey:@"drugForm"]]) {
                if (![[dic objectForKey:@"drugForm"] isEqualToString:@"颗粒"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"饮片"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"代煎"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"外用中药"]) {
                    NSString *mcjk = @"";
                    if (![self dataIsNull:[dic objectForKey:@"mcjk"]]) {
                        mcjk = [NSString stringWithFormat:@"%@", [dic objectForKey:@"mcjk"]];
                    }
                    
                    // 检查是否有包装数量信息，如果有则按照"每次X包装(Xg)"的格式显示
                    BOOL hasPackageInfo = NO;
                    
                    //如果为预览
                    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
                        NSString *drugForm = [NSString stringWithFormat:@"%@", [dic objectForKey:@"drugForm"]];
                        if ([drugForm isEqualToString:@"胶囊"] && ![self dataIsNull:[_orderDict objectForKey:@"mcDose"]]) {
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDoseUnit"]] ?: @"颗";
                            dayStr = [NSString stringWithFormat:@"每次 %@%@",[_orderDict objectForKey:@"mcDose"], mcDoseUnit];
                            capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                            dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"蜜丸"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_orderDict objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_orderDict objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"水丸"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_orderDict objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_orderDict objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"膏方"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_orderDict objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_orderDict objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                // 膏方剂型：根据包装单位判断是否为瓶装
                                if ([mcDoseUnit isEqualToString:@"瓶"]) {
                                    // 瓶装膏方：显示重量格式，不显示包装数量括号
                                    dayStr = [NSString stringWithFormat:@"每次 %@ g",mcjk];
                                } else {
                                    // 非瓶装膏方：显示包装数量格式
                                    dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                    capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                    dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                                }
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        }
                    }else if(self.medicatedInfoOrPreview == NSMedicatedInfo){
                        //从 000228 获取的详情
                        NSString *drugForm = [NSString stringWithFormat:@"%@", [dic objectForKey:@"drugForm"]];
                        if ([drugForm isEqualToString:@"胶囊"] && ![self dataIsNull:[_dataDic objectForKey:@"mcDose"]]) {
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDoseUnit"]] ?: @"颗";
                            dayStr = [NSString stringWithFormat:@"每次 %@%@",[_dataDic objectForKey:@"mcDose"], mcDoseUnit];
                            capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                            dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"蜜丸"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_dataDic objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_dataDic objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"水丸"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_dataDic objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_dataDic objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        } else if ([drugForm isEqualToString:@"膏方"]) {
                            NSString *mcDose = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDose"]];
                            NSString *mcDoseUnit = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"mcDoseUnit"]];
                            // 判断mcDose和mcDoseUnit是否都有有效值
                            if (![self dataIsNull:[_dataDic objectForKey:@"mcDose"]] && mcDose.length > 0 && ![self dataIsNull:[_dataDic objectForKey:@"mcDoseUnit"]] && mcDoseUnit.length > 0) {
                                // 膏方剂型：根据包装单位判断是否为瓶装
                                if ([mcDoseUnit isEqualToString:@"瓶"]) {
                                    // 瓶装膏方：显示重量格式，不显示包装数量括号
                                    dayStr = [NSString stringWithFormat:@"每次 %@ g",mcjk];
                                } else {
                                    // 非瓶装膏方：显示包装数量格式
                                    dayStr = [NSString stringWithFormat:@"每次 %@%@", mcDose, mcDoseUnit];
                                    capsuleNumStr = [NSString stringWithFormat:@"(%@g)", mcjk];
                                    dayStr = [dayStr stringByAppendingString:capsuleNumStr];
                                }
                            } else {
                                // 没有mcDose和mcDoseUnit字段或字段为空时，显示每次xg格式
                                dayStr = [NSString stringWithFormat:@"每次 %@g", mcjk];
                            }
                            hasPackageInfo = YES;
                        }
                        
                    }
                    
                    // 如果没有包装信息，则使用传统的重量显示格式
                    if (!hasPackageInfo) {
                        dayStr = [NSString stringWithFormat:@"每次 %@ g",mcjk];
                    }
                }
            }
            NSInteger capsuleNumLength = capsuleNumStr.length;
            NSMutableAttributedString *dayAttStr = [[NSMutableAttributedString alloc] initWithString:dayStr];
            [dayAttStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f] range:NSMakeRange(2, dayStr.length-1-2-capsuleNumLength)];
            
            NSRange capsuleNumRange = [dayStr rangeOfString:capsuleNumStr];
            
            if (capsuleNumRange.location != NSNotFound) {
                //找到匹配的
                [dayAttStr addAttributes:@{
                    NSForegroundColorAttributeName : [UIColor br_textRedColor]
                } range:capsuleNumRange];
            }
            
            CGSize daySize = [dayStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;

            CGRect dayRect = CGRectMake(allLabel.frame.origin.x+allLabel.frame.size.width+FONTSIZE(25), titleLabel.frame.origin.y, daySize.width, daySize.height);

            
            if (daySize.width > (view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18)-allSize.width-FONTSIZE(25))) {
                dayRect = CGRectMake(allLabel.frame.origin.x, allLabel.frame.origin.y+allLabel.frame.size.height+FONTSIZE(15), daySize.width, daySize.height);
            }
            UILabel *dayLabel = [self createLabel:dayRect text:nil];
            dayLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
            dayLabel.attributedText = dayAttStr;
            [view addSubview:dayLabel];
            
            NSString *timesStr = @"";
            NSUInteger timeStart = 3;
            if (![self dataIsNull:[dic objectForKey:@"preDays"]]) {
                timesStr = [NSString stringWithFormat:@"每剂分 %@ 次服用",[dic objectForKey:@"preDays"]];
            }
            NSUInteger timeEnd = timesStr.length-3-3;
            
            if (![self dataIsNull:[dic objectForKey:@"drugForm"]]) {
                if (![[dic objectForKey:@"drugForm"] isEqualToString:@"颗粒"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"饮片"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"代煎"] &&
                    ![[dic objectForKey:@"drugForm"] isEqualToString:@"外用中药"]) {
                    
                    NSString *teckDays = @"teckDays";
                    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
                        teckDays = @"teckDays";
                    }
                    else {
                        teckDays = @"eatDays";
                    }
                    
                    if (![self dataIsNull:[dic objectForKey:teckDays]]) {
                        teckDays = [dic objectForKey:teckDays];
                    }
                    timesStr = [NSString stringWithFormat:@"预计服用 %@ 天",teckDays];
                    timeStart = 4;
                    timeEnd = timesStr.length-4-1;
                }
            }
            NSMutableAttributedString *timesAttStr = [[NSMutableAttributedString alloc] initWithString:timesStr];
            [timesAttStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f] range:NSMakeRange(timeStart, timeEnd)];
            CGSize timesSize = [timesStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            UILabel *timesLabel = [self createLabel:CGRectMake(titleLabel.frame.origin.x+titleLabel.frame.size.width+FONTSIZE(18), dayLabel.frame.origin.y+dayLabel.frame.size.height+FONTSIZE(15), timesSize.width, timesSize.height) text:nil];
            timesLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
            timesLabel.attributedText = timesAttStr;
            [view addSubview:timesLabel];
            
            NSString *eatTimeStr = @"";
            if (![self dataIsNull:[dic objectForKey:@"eatTime"]]) {
                eatTimeStr = [NSString stringWithFormat:@"%@服用",[dic objectForKey:@"eatTime"]];
            }
            
            NSString *timeNum = [self findNumFromStr:eatTimeStr];
            NSArray *timeArr = [eatTimeStr componentsSeparatedByString:timeNum];
            if (timeArr.count == 2) {
                eatTimeStr = [NSString stringWithFormat:@"%@ %@ %@",timeArr[0],timeNum,timeArr[1]];
            }
            NSRange rangeTime = [eatTimeStr rangeOfString:timeNum];
            if (rangeTime.location == NSNotFound) {
                rangeTime = NSMakeRange(0, 0);
            }
            
            NSMutableAttributedString *eatTimeAttStr = [[NSMutableAttributedString alloc] initWithString:eatTimeStr];
            [eatTimeAttStr addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f] range:NSMakeRange(rangeTime.location, rangeTime.length)];
            CGSize eatTimeSize = [eatTimeStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            
            CGRect eatTimeRect = CGRectMake(timesLabel.frame.origin.x+timesLabel.frame.size.width+FONTSIZE(25), timesLabel.frame.origin.y, eatTimeSize.width, eatTimeSize.height);
            
            if (eatTimeSize.width > (view.frame.size.width-FONTSIZE(15)*2-size.width-FONTSIZE(18)-timesSize.width-FONTSIZE(25))) {
                eatTimeRect = CGRectMake(timesLabel.frame.origin.x, timesLabel.frame.origin.y+timesLabel.frame.size.height+FONTSIZE(15), eatTimeSize.width, eatTimeSize.height);
            }
            UILabel *eatTimeLabel = [self createLabel:eatTimeRect text:nil];
            eatTimeLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
            eatTimeLabel.attributedText = eatTimeAttStr;
            [view addSubview:eatTimeLabel];
            
            heightY = eatTimeLabel.frame.origin.y + eatTimeLabel.frame.size.height + FONTSIZE(15);
            
        } else {
            
            infoLabel = [[UILabel alloc] initWithFrame:CGRectMake(titleLabel.frame.origin.x+titleLabel.frame.size.width+FONTSIZE(18), titleLabel.frame.origin.y, size2.width, size2.height)];
            infoLabel.font = FONT_Light(16);
            infoLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
            infoLabel.numberOfLines = 0;
            infoLabel.text = infoStr;
            [view addSubview:infoLabel];
            
            if (self.medicatedInfoOrPreview == NSMedicatedInfo) {
                if (i == arr.count-1) {
                    infoLabel.textColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
                }
            }
            
            
            if (i == 3 && arr.count == 4) {
                UILabel *label = [[UILabel alloc] init];
                label.font = kFontLight(14);
                label.textColor = [UIColor br_textRedColor];
                label.text = @"药房发货时如有疑问会与您电话联系。";
                [view addSubview:label];
                
                [label mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.mas_equalTo(@(kHorizontalMargin));
                    make.top.equalTo(infoLabel.mas_bottom).with.offset(5);
                }];
            }
            
            
        }
        
    }
    
    if (self.btnShowOrNot == NSBtnNotShow) {
        if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
            
            CGSize sizeMoney = [@"订单金额:" boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            CGSize sizeDocTime = [@"医      师:" boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            
            NSString *moneyStr = @"";
            if (![self dataIsNull:[dic objectForKey:@"amount"]]) {
                moneyStr = [NSString stringWithFormat:@"¥%@",[dic objectForKey:@"amount"]];
            }
            
            NSString *docStr = [UserManager shareInstance].getName;
            
            NSString *timeStr = @"";
            if (![self dataIsNull:[dic objectForKey:@"creatTime"]]) {
                timeStr = [NSString stringWithFormat:@"%@",[dic objectForKey:@"creatTime"]];
            }
            CGSize sizeMoney1 = [moneyStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-sizeMoney.width-FONTSIZE(3), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            CGSize sizeDoc = [docStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-sizeDocTime.width-FONTSIZE(3), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            CGSize sizeTime = [timeStr boundingRectWithSize:CGSizeMake(view.frame.size.width-FONTSIZE(15)*2-sizeDocTime.width-FONTSIZE(3), 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
            
            float finalFrameX = self.view.frame.size.width - FONTSIZE(15);
            float moneyW = sizeMoney1.width;
            float docW = sizeDoc.width;
            float timeW = sizeTime.width;
            
            if (moneyW > docW) {
                
                if (moneyW > timeW) {
                    finalFrameX -= moneyW;
                } else {
                    finalFrameX -= timeW;
                }
                
            } else {
                
                if (docW > timeW) {
                    finalFrameX -= docW;
                } else {
                    finalFrameX -= timeW;
                }
            }
            
            NSArray *mdtArr = @[@"订单金额:",@"医      师:",@"日      期:"];
            NSArray *mdtInfoArr = @[moneyStr,docStr,timeStr];
            NSArray *leftLabelWArr = @[[NSNumber numberWithFloat:sizeMoney.width+1],[NSNumber numberWithFloat:sizeDocTime.width+1],[NSNumber numberWithFloat:sizeDocTime.width+1]];
            NSArray *rightLabelWArr = @[[NSNumber numberWithFloat:sizeMoney1.width+1],[NSNumber numberWithFloat:sizeDoc.width+1],[NSNumber numberWithFloat:sizeTime.width+1]];
            
            heightY += FONTSIZE(5);
            
            for (int i = 0; i < mdtArr.count; i++) {
                UILabel *leftLabel = [[UILabel alloc] initWithFrame:CGRectMake(finalFrameX-FONTSIZE(3)-[leftLabelWArr[i] floatValue], heightY+FONTSIZE(15), [leftLabelWArr[i] floatValue], sizeMoney.height)];
                leftLabel.font = FONT_Light(16);
                leftLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
                leftLabel.text = mdtArr[i];
                [view addSubview:leftLabel];
                
                UILabel *rightLabel = [[UILabel alloc] initWithFrame:CGRectMake(finalFrameX, leftLabel.frame.origin.y, [rightLabelWArr[i] floatValue], leftLabel.frame.size.height)];
                rightLabel.font = FONT_Light(16);
                rightLabel.textColor = [UIColor colorWithRed:26.0/255 green:26.0/255 blue:26.0/255 alpha:1.f];
                rightLabel.numberOfLines = 0;
                rightLabel.text = mdtInfoArr[i];
                [view addSubview:rightLabel];
                
                heightY = leftLabel.frame.origin.y + leftLabel.frame.size.height;
            }
            
        }
    }
    
    view.frame = CGRectMake(frame.origin.x, frame.origin.y, frame.size.width, heightY+FONTSIZE(24));
    
    return view;
}

/*
 DCID 是药房代码
 */
- (NSString *)getInfoStrWithDict:(NSDictionary *)dic index:(NSInteger)i title:(NSString *)titleStr isShowDCID:(BOOL)isShowDCID{
    
    NSString *infoStr = @"";
    
    //药房代码
    NSString *DCID = @"药房正在分配中";
    if (![self dataIsNull:[dic objectForKey:@"dcId"]]) {
        DCID = [NSString stringWithFormat:@"%@", [dic objectForKey:@"dcId"]];
    }
    
    //药材类型
    NSString *drugForm = @"";
    if (![self dataIsNull:[dic objectForKey:@"drugForm"]]) {
        drugForm = [NSString stringWithFormat:@"%@", [dic objectForKey:@"drugForm"]];
        if (![self dataIsNull:[dic objectForKey:@"productProviteName"]]) {
            drugForm = [NSString stringWithFormat:@"%@(%@)",drugForm,[NSString stringWithFormat:@"%@", [dic objectForKey:@"productProviteName"]]];
            
        }
    }
    
    //用药方法
    NSString *useMethod = @"";
    if (![self dataIsNull:[dic objectForKey:@"mode"]]) {
        useMethod = [NSString stringWithFormat:@"%@", [dic objectForKey:@"mode"]];
    }
    
    //服药禁忌
    NSString *contraindication = @"无";
    if (![self dataIsNull:[dic objectForKey:@"contraindication"]]) {
        NSString *contraindicationStr = [NSString stringWithFormat:@"%@", [dic objectForKey:@"contraindication"]];
        
        if (contraindicationStr && contraindicationStr.length) {
            
            contraindication = contraindicationStr;
            
        }
    }
    
    //补充说明
    NSString *remark = @"无";
    if (![self dataIsNull:[dic objectForKey:@"remak"]]) {
        NSString *remakStr = [NSString stringWithFormat:@"%@", [dic objectForKey:@"remak"]];
        if (remakStr && remakStr.length) {
            
            remark = remakStr;
            
        }
    }
    
    //是否使用药房代煎
    NSString *boilMedicineService = @"";
    if (![self dataIsNull:[dic objectForKey:@"isDaiJian"]]) {
        NSString *daijian = [NSString stringWithFormat:@"%@", [dic objectForKey:@"isDaiJian"]];
        if ([daijian isEqualToString:@"0"]) {
            boilMedicineService = @"否";
        }
        else if ([daijian isEqualToString:@"1"]) {
            boilMedicineService = @"代煎";
        }
    }
    
    //订单金额
    NSString *amount = @"";
    if (![self dataIsNull:[dic objectForKey:@"amount"]]) {
        amount = [NSString stringWithFormat:@"¥%@",[dic objectForKey:@"amount"]];
    }
    
    //代煎偏好
    NSString *makeMethod = @"";
    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
        //如果为预览
        if (![self dataIsNull:[_orderDict objectForKey:@"makeMethod"]]) {
            makeMethod = [NSString stringWithFormat:@"%@",[_orderDict objectForKey:@"makeMethod"]];
        }
    } else if (self.medicatedInfoOrPreview == NSMedicatedInfo){
        //从 000228 接口获取的订单数据
        if (![self dataIsNull:[_dataDic objectForKey:@"makeMethod"]]) {
            makeMethod = [NSString stringWithFormat:@"%@",[_dataDic objectForKey:@"makeMethod"]];
        }
    }
    
    //保密类型
    NSString *secrecyType = @"正常处方";
    if (![self dataIsNull:[dic objectForKey:@"isSecrecy"]]) {
        NSString *isSecrecy = [NSString stringWithFormat:@"%@", [dic objectForKey:@"isSecrecy"]];
        if ([isSecrecy isEqualToString:@"1"]) {
            secrecyType = @"保密处方";
        } else if ([isSecrecy isEqualToString:@"2"]) {
            secrecyType = @"剂量保密";
        }
    }
    // 如果是预览状态,从 orderDict 中获取 isSecrecy
    if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
        if (![self dataIsNull:[_orderDict objectForKey:@"isSecrecy"]]) {
            NSString *isSecrecy = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"isSecrecy"]];
            if ([isSecrecy isEqualToString:@"1"]) {
                secrecyType = @"保密处方";
            } else if ([isSecrecy isEqualToString:@"2"]) {
                secrecyType = @"剂量保密";
            }
        }
    }
    
    if ([titleStr isEqualToString:@"药房代码"]) {
        infoStr = DCID;
    }
    else if ([titleStr isEqualToString:@"药方剂型"]) {
        infoStr = drugForm;
    }
    else if ([titleStr isEqualToString:@"规格"]) {
        // 规格信息
        NSString *specInfo = @"";
        if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
            // 预览状态从 orderDict 获取
            specInfo = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"packageSpec"]];
        } else {
            // 详情状态从 dataDic 获取
            specInfo = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"packageSpec"]];
        }
        infoStr = ![self dataIsNull:specInfo] ? specInfo : @"";
    }
    else if ([titleStr isEqualToString:@"辅料"]) {
        // 辅料信息
        NSString *materialInfo = @"";
        if (self.medicatedInfoOrPreview == NSMedicatedPreview) {
            // 预览状态从 orderDict 获取
            materialInfo = [NSString stringWithFormat:@"%@", [_orderDict objectForKey:@"makeMaterial"]];
        } else {
            // 详情状态从 dataDic 获取
            materialInfo = [NSString stringWithFormat:@"%@", [_dataDic objectForKey:@"makeMaterial"]];
        }
        if ([materialInfo isEqualToString:@"(null)"] || [materialInfo isEqualToString:@"<null>"]) {
            materialInfo = @"";
        }
        materialInfo = [materialInfo stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
        infoStr = materialInfo.length > 0 ? materialInfo : @"";
    }
//    else if ([titleStr isEqualToString:@"用法用量"]) {
//
//    }
    else if ([titleStr isEqualToString:@"服药禁忌"]) {
        infoStr = contraindication;
    }
    else if ([titleStr isEqualToString:@"补充说明"]) {
        infoStr = remark;
    }
    else if ([titleStr isEqualToString:@"订单金额"]) {
        infoStr = amount;
    }
    else if ([titleStr isEqualToString:@"是否代煎"]) {
        infoStr = boilMedicineService;
    }
    else if ([titleStr isEqualToString:@"代煎偏好"]) {
        infoStr = makeMethod;
    }
    else if ([titleStr isEqualToString:@"保密类型"]) {
        infoStr = secrecyType;
    }else if ([titleStr isEqualToString:@"用药方法"]) {
        infoStr = useMethod;
    }
    
    
//    if (isShowDCID) {
//
//        if (i == 0) {
//            infoStr = DCID;
//        }else if (i == 1) {
//            infoStr = drugForm;
//        }else if (i == 3){
//            infoStr = contraindication;
//        }else if (i == 4){
//            infoStr = remark;
//        }else if (i == 5) {
//
//
//
////            infoStr = amount;
//            infoStr = boilMedicineService;
//        }
//        else if (i == 6) {
//            infoStr = amount;
//        }
//        else if (i == 7) {
//
//        }
//
//    }
//    else {
//
//        if (i == 0) {
//            infoStr = drugForm;
//        }else if (i == 2){
//            infoStr = contraindication;
//        }else if (i == 3){
//            infoStr = remark;
//        }else if (i == 4) {
////            infoStr = amount;
//            infoStr = boilMedicineService;
//        }
//        else if (i == 5) {
//            infoStr = amount;
//        }
//
//    }
    
    return infoStr;
    
}

//取出数字
-(NSString *)findNumFromStr:(NSString *)str {
    
    NSMutableString *numberString = [[NSMutableString alloc] init];
    NSString *tempStr = @"";
    NSScanner *scanner = [NSScanner scannerWithString:str];
    NSCharacterSet *numbers = [NSCharacterSet characterSetWithCharactersInString:@"0123456789."];
    
    while (![scanner isAtEnd]) {
        
        [scanner scanUpToCharactersFromSet:numbers intoString:NULL];
        
        [scanner scanCharactersFromSet:numbers intoString:&tempStr];
        [numberString appendString:tempStr];
        tempStr = @"";
    }
    
    return numberString;
}

#pragma mark - 作废，修改剂数，复制药方到添加药材
- (void)downView {
    
    CGFloat btnHeight = 57;
    CGFloat leftBtnWidth = (isiPhone4 || isiPhone5) ? 80 : 90;
    //底部按钮背景
    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bgView];
    
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.equalTo(self.view);
        make.bottom.equalTo(self.view).with.offset(-kTabbarSafeBottomMargin);
        make.height.mas_equalTo(@(btnHeight));
    }];
    
    UIView *lineView = [[UIView alloc] init];
    lineView.backgroundColor = [UIColor br_topOrBottomLineColor];
    [bgView addSubview:lineView];
    
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.and.top.equalTo(bgView);
        make.height.mas_equalTo(@(1/kScreenScale));
    }];
    
    //作废
    NSString *title = @"作废";
    UIImage *delImage = [UIImage imageNamed:@"delete_icon"];
    self.removeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.removeBtn setTitle:title forState:UIControlStateNormal];
    [[self.removeBtn titleLabel] setFont:kFontLight(13)];
    [self.removeBtn setTitleColor:[UIColor colorWithHex:0x1a1a1a] forState:UIControlStateNormal];
    [self.removeBtn setImage:delImage forState:UIControlStateNormal];
    [bgView addSubview:self.removeBtn];
    
    CGSize titleSize = CGSizeMake([title widthForFont:kFontLight(13)], 13);
    CGSize imageSize = delImage.size;
    
    
    [self.removeBtn setImageEdgeInsets:UIEdgeInsetsMake(-imageSize.height / 2, titleSize.width / 2, imageSize.height / 2, -titleSize.width / 2)];
    [self.removeBtn setTitleEdgeInsets:UIEdgeInsetsMake(titleSize.height / 2 + 2, -imageSize.width / 2, -titleSize.height / 2 - 2, imageSize.width / 2)];
    
    [self.removeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.top.and.bottom.equalTo(bgView);
        make.width.mas_equalTo(@(leftBtnWidth));
    }];
    
    [self.removeBtn addTarget:self action:@selector(removeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    
    //修改剂数
    UIImage *editNormalImage = [UIImage imageNamed:@"edit_normal_icon"];
    UIImage *editDisableImage = [UIImage imageNamed:@"edit_disable_icon"];
    
    title = @"修改剂数";
    
    self.refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.refreshBtn setTitle:title forState:UIControlStateNormal];
    [[self.refreshBtn titleLabel] setFont:kFontLight(13)];
    [self.refreshBtn setTitleColor:[UIColor colorWithHex:0x1a1a1a] forState:UIControlStateNormal];
    [self.refreshBtn setImage:editNormalImage forState:UIControlStateNormal];
    [self.refreshBtn setImage:editDisableImage forState:UIControlStateDisabled];
    [bgView addSubview:self.refreshBtn];
    
    titleSize = CGSizeMake([title widthForFont:kFontLight(13)], 13);
    imageSize = editNormalImage.size;
    
    [self.refreshBtn setImageEdgeInsets:UIEdgeInsetsMake(-imageSize.height / 2, titleSize.width / 2, imageSize.height / 2, -titleSize.width / 2)];
    [self.refreshBtn setTitleEdgeInsets:UIEdgeInsetsMake(titleSize.height / 2 + 2, -imageSize.width / 2, -titleSize.height / 2 - 2, imageSize.width / 2)];
    
    [self.refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.and.bottom.equalTo(bgView);
        make.left.equalTo(self.removeBtn.mas_right);
        make.width.mas_equalTo(@(leftBtnWidth));
    }];
    
    [self.refreshBtn addTarget:self action:@selector(refreshBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //分割线
    UIView *spaceLineView = [[UIView alloc] init];
    spaceLineView.backgroundColor = [UIColor br_insideDivisionLineColor];
    [bgView addSubview:spaceLineView];
    
    [spaceLineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(@(10));
        make.width.mas_equalTo(@(1/kScreenScale));
        make.height.mas_equalTo(@(20));
        make.left.mas_equalTo(@(leftBtnWidth));
    }];
    
    //复制药方 ——> 添加药材
    UIImage *rightArrowImage = [UIImage imageNamed:@"right_arrow_icon"];
    title = @"复制药方     添加药材";
    self.prescriptionCopyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [[self.prescriptionCopyBtn titleLabel] setFont:kFontRegular(16)];
    [self.prescriptionCopyBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setTitle:title forState:UIControlStateNormal];
    [bgView addSubview:self.prescriptionCopyBtn];
    
    [self.prescriptionCopyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.and.right.and.bottom.equalTo(bgView);
        make.left.equalTo(self.refreshBtn.mas_right);
    }];
    
    
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:rightArrowImage];
    [self.prescriptionCopyBtn addSubview:arrowImageView];
    
    [arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(rightArrowImage.size);
        make.center.equalTo(self.prescriptionCopyBtn);
    }];
    
    [self.prescriptionCopyBtn addTarget:self action:@selector(copyPrescriptionBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
}

#pragma mark - 用药预览底部视图
- (void)downPreviewView {
    
    NSString *btnText = @"发送给患者";
    
    if (self.prescribeType == BRPrescribeTypeQuick) {
        btnText = @"发送到患者微信";
    }else if (self.prescribeType == BRPrescribeTypeMessage) {
        btnText = @"发送到患者短信";
    }
    
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    btn.frame = CGRectMake(0, self.view.frame.size.height-self.navigationController.navigationBar.frame.origin.y-self.navigationController.navigationBar.frame.size.height-FONTSIZE(45) - kTabbarSafeBottomMargin, self.view.frame.size.width, FONTSIZE(45));
    btn.backgroundColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
    [btn setTitle:btnText forState:UIControlStateNormal];
    [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    btn.titleLabel.font = FONT_Regular(17);
    [btn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:btn];
    
}

#pragma mark - 复制药方底部
- (void)downCopyPrescriptionView {
    
    CGFloat btnHeight = 57;
    
    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bgView];
    
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.equalTo(self.view);
        make.bottom.equalTo(self.view).with.offset(-kTabbarSafeBottomMargin);
        make.height.mas_equalTo(@(btnHeight));
    }];
    
    //复制药方 ——> 添加药材
    UIImage *rightArrowImage = [UIImage imageNamed:@"right_arrow_icon"];
    NSString * title = @"复制药方     添加药材";
    self.prescriptionCopyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [[self.prescriptionCopyBtn titleLabel] setFont:kFontRegular(16)];
    [self.prescriptionCopyBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setTitle:title forState:UIControlStateNormal];
    [bgView addSubview:self.prescriptionCopyBtn];
    
    [self.prescriptionCopyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(bgView);
    }];
    
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:rightArrowImage];
    [self.prescriptionCopyBtn addSubview:arrowImageView];
    
    [arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(rightArrowImage.size);
        make.center.equalTo(self.prescriptionCopyBtn);
    }];
    
    [self.prescriptionCopyBtn addTarget:self action:@selector(copyPrescriptionBtnClick) forControlEvents:UIControlEventTouchUpInside];
}

- (void)downEditAndCopyPrescriptionView {
    
    CGFloat btnHeight = 57;
    CGFloat leftBtnWidth = 90;
    
    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bgView];
    
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.equalTo(self.view);
        make.bottom.equalTo(self.view).with.offset(-kTabbarSafeBottomMargin);
        make.height.mas_equalTo(@(btnHeight));
    }];
    
    //作废
    NSString *title = @"作废";
    UIImage *delImage = [UIImage imageNamed:@"delete_icon"];
    self.removeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.removeBtn setTitle:title forState:UIControlStateNormal];
    [[self.removeBtn titleLabel] setFont:kFontLight(13)];
    [self.removeBtn setTitleColor:[UIColor colorWithHex:0x1a1a1a] forState:UIControlStateNormal];
    [self.removeBtn setImage:delImage forState:UIControlStateNormal];
    [bgView addSubview:self.removeBtn];
    
    CGSize titleSize = CGSizeMake([title widthForFont:kFontLight(13)], 13);
    CGSize imageSize = delImage.size;
    
    
    [self.removeBtn setImageEdgeInsets:UIEdgeInsetsMake(-imageSize.height / 2, titleSize.width / 2, imageSize.height / 2, -titleSize.width / 2)];
    [self.removeBtn setTitleEdgeInsets:UIEdgeInsetsMake(titleSize.height / 2 + 2, -imageSize.width / 2, -titleSize.height / 2 - 2, imageSize.width / 2)];
    
    [self.removeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.top.and.bottom.equalTo(bgView);
        make.width.mas_equalTo(@(leftBtnWidth));
    }];
    
    [self.removeBtn addTarget:self action:@selector(removeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //复制药方 ——> 添加药材
    UIImage *rightArrowImage = [UIImage imageNamed:@"right_arrow_icon"];
    title = @"复制药方     添加药材";
    self.prescriptionCopyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [[self.prescriptionCopyBtn titleLabel] setFont:kFontRegular(16)];
    [self.prescriptionCopyBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [self.prescriptionCopyBtn setTitle:title forState:UIControlStateNormal];
    [bgView addSubview:self.prescriptionCopyBtn];
    
    [self.prescriptionCopyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.and.right.and.bottom.equalTo(bgView);
        make.left.equalTo(self.removeBtn.mas_right);
    }];
    
    
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:rightArrowImage];
    [self.prescriptionCopyBtn addSubview:arrowImageView];
    
    [arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(rightArrowImage.size);
        make.center.equalTo(self.prescriptionCopyBtn);
    }];
    
    [self.prescriptionCopyBtn addTarget:self action:@selector(copyPrescriptionBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
}

- (void)bottomCancelAndSendView {
    
    CGFloat btnHeight = 57;
    CGFloat leftBtnWidth = 90;
    
    UIView *bgView = [[UIView alloc] init];
    bgView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:bgView];
    
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.right.equalTo(self.view);
        make.bottom.equalTo(self.view).with.offset(-kTabbarSafeBottomMargin);
        make.height.mas_equalTo(@(btnHeight));
    }];
    
    //作废
    NSString *title = @"作废";
    UIImage *delImage = [UIImage imageNamed:@"delete_icon"];
    self.removeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.removeBtn setTitle:title forState:UIControlStateNormal];
    [[self.removeBtn titleLabel] setFont:kFontLight(13)];
    [self.removeBtn setTitleColor:[UIColor colorWithHex:0x1a1a1a] forState:UIControlStateNormal];
    [self.removeBtn setImage:delImage forState:UIControlStateNormal];
    [bgView addSubview:self.removeBtn];
    
    CGSize titleSize = CGSizeMake([title widthForFont:kFontLight(13)], 13);
    CGSize imageSize = delImage.size;
    
    
    [self.removeBtn setImageEdgeInsets:UIEdgeInsetsMake(-imageSize.height / 2, titleSize.width / 2, imageSize.height / 2, -titleSize.width / 2)];
    [self.removeBtn setTitleEdgeInsets:UIEdgeInsetsMake(titleSize.height / 2 + 2, -imageSize.width / 2, -titleSize.height / 2 - 2, imageSize.width / 2)];
    
    [self.removeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.and.top.and.bottom.equalTo(bgView);
        make.width.mas_equalTo(@(leftBtnWidth));
    }];
    

    [self.removeBtn addTarget:self action:@selector(removeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    UIButton *sendButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [[sendButton titleLabel] setFont:kFontRegular(16)];
    [sendButton setTitle:@"发  送" forState:UIControlStateNormal];
    [sendButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [sendButton setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [bgView addSubview:sendButton];
    
    [sendButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.and.right.and.bottom.equalTo(bgView);
        make.left.equalTo(self.removeBtn.mas_right);
    }];
    
    sendButton.rac_command = self.sendCommand;
    
}

#pragma mark  发送给小然

- (void)presSendToXiaoRan{
    
    NSDictionary *dict = @{@"method_code" : @"000376",
                           @"orderId":_orderId,
                           @"operateType":@"1",
                           @"userId":[[UserManager shareInstance] getUserId]
                           };
    
    RACCommand *command = [[RACCommand alloc]initWithSignalBlock:^RACSignal * _Nonnull(id  _Nullable input) {
        
        return [RACSignal createSignal:^RACDisposable * _Nullable(id<RACSubscriber>  _Nonnull subscriber) {
            
            [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
                
                __weak typeof(self) mySelf = self;
                
                NSString *code = [responseObject objectForKey:@"code"];
                if ([code isEqualToString:@"0000"]) {
                    [subscriber sendNext:@"success"];
                }
                else {
                    
                    NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                    NSString *str = [NSString stringWithFormat:@"%@",errorMsg];
                    [self.view makeToast:str duration:kToastDuration position:CSToastPositionCenter];
                    [subscriber sendNext:@"error"];
                }
                
                [subscriber sendCompleted];
                
            } failure:^(NSURLSessionDataTask *task, NSError *error) {
                
                [subscriber sendNext:@"failed"];
                [self.view makeToast:@"网络请求失败" duration:kToastDuration position:CSToastPositionCenter];
                
                [subscriber sendCompleted];
                
            }];
            
            return nil;
            
        }];
        
    }];
    
    _sendCommand = command;
    
    [[_sendCommand.executing skip:1]subscribeNext:^(NSNumber * _Nullable x) {
        
        if ([x boolValue]) {
            
            //正在执行
            self.hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
            
        }
        else {
            
            //执行完毕
            [self.hud removeFromSuperview];
            [self.hud hideAnimated:NO];
            
        }
        
    }];
    
    [_sendCommand.executionSignals.switchToLatest subscribeNext:^(id  _Nullable x) {
        
        if ([x isEqualToString:@"success"]) {
            
            //发送成功
            [self.view makeToast:@"发送成功" duration:kToastDuration position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                [self.navigationController popViewControllerAnimated:YES];
            }];
            
        }
        else {
            
            //发送失败
            [self.view makeToast:@"发送失败" duration:kToastDuration position:CSToastPositionCenter];
            
        }
        
    }];
    
}

- (void)btnClick:(UIButton *)sender {
    
    NSDictionary *dict = @{@"method_code" : @"000045"
                           };
    
    __block MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
    __weak MedicatedInfoViewController *weakSelf = self;
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        
        __weak typeof(self) mySelf = self;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            DoctorAuthentiationModel *model = [DoctorAuthentiationModel mj_objectWithKeyValues:responseObject];
            
            //之前认证成功或者当前认证成功  直接发送
            if ([model.isAuthentication isEqualToString:@"1"] || model.currentAuthenticationState == DoctorAuthentiationStateSuccess) {
                [self sendPatientWithHud:hud];
            }
            //试用期之内
            else if ([model.stockDay intValue] > 0) {
                //未上传资质认证
                if (model.currentAuthenticationState == DoctorAuthentiationStateNotAuth) {
                    
                    [hud hideAnimated:NO];
                    
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    [alertView.okButton setTitle:@"立即认证" forState:UIControlStateNormal];
                    [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
                    
                    NSString *message = [NSString stringWithFormat:@"您的试用期剩余%@天，为了不影响您的正常使用，请尽快上传资质认证。",model.stockDay];
                    
                    [alertView showAlertViewWithCancelButton:message attributes:@{NSForegroundColorAttributeName : [UIColor br_textRedColor],NSFontAttributeName : kFontLight(16)} range:NSMakeRange(7, model.stockDay.length) completion:^(BOOL isOk) {
                        //认证
                        if (isOk) {
                            [self certification:@"3"];
                        }
                        //发送给患者
                        else {
                            
                            hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
                            
                            [self sendPatientWithHud:hud];
                        }
                    }];
                }
                //已上传资质认知  等待审核   审核中
                else if (model.currentAuthenticationState == DoctorAuthentiationStateReview) {
                    //直接发送
                    [self sendPatientWithHud:hud];
                }
                //审核失败
                else if (model.currentAuthenticationState == DoctorAuthentiationStateFailed) {
                    
                    [hud hideAnimated:NO];
                    
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    [alertView.okButton setTitle:@"立即认证" forState:UIControlStateNormal];
                    [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
                    
                    NSString *message = [NSString stringWithFormat:@"认证审核失败，试用期剩余%@天，为了不影响您的正常使用，请重新上传资质认证。",model.stockDay];
                    
                    [alertView showAlertViewWithCancelButton:message attributes:@{NSForegroundColorAttributeName : [UIColor br_textRedColor], NSFontAttributeName : kFontLight(16)} range:NSMakeRange(12, model.stockDay.length) completion:^(BOOL isOk) {
                        //认证
                        if (isOk) {
                            [self certification:@"4"];
                        }
                        //发送给患者
                        else {
                            hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
                            [self sendPatientWithHud:hud];
                        }
                        
                    }];
                }
            }
            //已过试用期
            else if ([model.stockDay intValue] <= 0) {
                
                [hud hideAnimated:NO];
                
                //未上传资质认证
                if (model.currentAuthenticationState == DoctorAuthentiationStateNotAuth) {
                    
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    [alertView.okButton setTitle:@"立即认证" forState:UIControlStateNormal];
                    [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
                    
                    NSString *message = [NSString stringWithFormat:@"认证通过后可使用该功能。"];
                    
                    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
                        //认证
                        if (isOk) {
                            [self certification:@"3"];
                        }
                    }];
                }
                //审核失败
                else if (model.currentAuthenticationState == DoctorAuthentiationStateFailed) {
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    [alertView.okButton setTitle:@"立即认证" forState:UIControlStateNormal];
                    [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
                    
                    NSString *message = [NSString stringWithFormat:@"认证审核失败，请重新认证。"];
                    
                    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
                        //认证
                        if (isOk) {
                            [self certification:@"4"];
                        }
                    }];
                }
                //已上传资质认证
                else if (model.currentAuthenticationState == DoctorAuthentiationStateReview) {
                    
                    BRAlertView *alertView = [[BRAlertView alloc] init];
                    alertView.isHideWhenTapBackground = YES;
                    
                    NSString *message = @"认证审核中,审核通过后可使用该功能。";
                    
                    __weak BRAlertView *weakAlertView = alertView;
                    [alertView showAlertView:message completion:^{
                        [weakAlertView close];
                    }];
                    
                }
            }
        }
        else {
            
            [hud hideAnimated:NO];
            
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSString *str = [NSString stringWithFormat:@"%@",errorMsg];
            [self.view makeToast:str duration:kToastDuration position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        [hud hideAnimated:NO];
        
        [self.view makeToast:@"网络请求失败" duration:kToastDuration position:CSToastPositionCenter];
    }];
    
}

#pragma mark - 前往认证
- (void)certification:(NSString *)successful {
    self.tabBarController.tabBar.hidden = YES;
    self.navigationController.navigationBar.hidden = NO;
    QualificationCertificationViewController *qualificationVC = [[QualificationCertificationViewController alloc] init];
    qualificationVC.successful = successful;
    [self.navigationController pushViewController:qualificationVC animated:YES];
    
}
- (void)sendPatientWithHud:(MBProgressHUD *)hud {
    //快速开方
    if (self.prescribeType == BRPrescribeTypeQuick) {
        
        NSString *prescriptionStr = [_orderDict mj_JSONString];
        NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:@{
            @"method_code" : @"000440",
            @"apiVer" : @"4",
            @"order" : prescriptionStr,
            @"smsOrder" : @"0"
        }];
        
        __weak __typeof(self)weakSelf = self;
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            [hud removeFromSuperview];
            [hud hideAnimated:NO];
            
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                NSString *shareUrl = [responseObject objectForKey:@"shareUrl"];
                NSString *preId = [responseObject objectForKey:@"preId"];
                
                NSLog(@"share url == %@",shareUrl);
                
                [weakSelf shareContentWithShareUrl:shareUrl preId:preId];
            }
            else{
                NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
            }
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            [weakSelf.view makeToast:@"请求失败，请稍后重试!" duration:2 position:CSToastPositionCenter];
        }];
        
        
    }else if (self.prescribeType == BRPrescribeTypeMessage) {
        
        [hud removeFromSuperview];
        [hud hideAnimated:YES];
        //输入手机号 发送
        
        __weak typeof(self) weakSelf = self;
        
       __block BRAlertView *alertView = [Utils br_showAlertHasMobileInputMessage:@"请输入患者手机号" mobileText:@"" okButtonTitle:@"发送" placeholder:@"输入手机号" completion:^(BOOL isOk, NSString *text) {
            if (isOk) {
                NSLog(@"输入的手机号 == %@",text);
                
                if ([text length] == 0) {
                    [alertView makeToast:@"请输入患者手机号码" duration:2 position:CSToastPositionCenter];
                }else if ([text length] < 11) {
                    [alertView makeToast:@"请输入11位手机号" duration:2 position:CSToastPositionCenter];
                }else {
//                    [alertView close];
                    [alertView close];
                    
                    //进行发送
                    NSString *prescriptionStr = [_orderDict mj_JSONString];
                    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithDictionary:@{
                        @"method_code" : @"000440",
                        @"apiVer" : @"4",
                        @"order" : prescriptionStr,
                        @"smsOrder" : @"1"
                    }];
                    
                    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
                    } success:^(NSURLSessionDataTask *task, id responseObject) {
                        [hud removeFromSuperview];
                        [hud hideAnimated:NO];
                        
                        NSString *code = [responseObject objectForKey:@"code"];
                        if ([code isEqualToString:@"0000"]) {
                            NSString *shareUrl = [responseObject objectForKey:@"shareUrl"];
                            NSString *preId = [responseObject objectForKey:@"preId"];
                            
                            
                            NSLog(@"share url == %@",shareUrl);
                            
//                            [weakSelf shareContentWithShareUrl:shareUrl preId:preId];
                            [weakSelf messagePrescribeShareOrderId:preId mobile:text];
                        }
                        else{
                            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                            [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
                        }
                    } failure:^(NSURLSessionDataTask *task, NSError *error) {
                        [weakSelf.view makeToast:@"请求失败，请稍后重试!" duration:2 position:CSToastPositionCenter];
                    }];
                    
                }
            }
        }];
        
    }else {
        
        NSString *prescriptionStr = [_orderDict mj_JSONString];
        
        NSDictionary *dict = @{
                               @"method_code":@"000212",
                               @"order":prescriptionStr,
                               @"apiVer" : @"4"
                               };
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            [hud removeFromSuperview];
            [hud hideAnimated:NO];
            
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                
                [[[UIApplication sharedApplication] delegate].window makeToast:@"发送成功" duration:kToastDuration position:CSToastPositionCenter];
                
                //发送成功
                [self.navigationController popViewControllerAnimated:YES];
                if (_sendSuccessBlock) {
                    _sendSuccessBlock();
                }
                
            }
            else {
                
                NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
                [self.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
                
            }
            
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            
            [hud removeFromSuperview];
            [hud hideAnimated:NO];
            
            [self.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
            
        }];
    }
}

#pragma mark - 短信开方发送��信
- (void)messagePrescribeShareOrderId:(NSString *)orderId mobile:(NSString *)mobile {
        
    NSDictionary *dict = @{
        @"method_code" : @"000446",
        @"apiVer" : @"4",
        @"orderId" : orderId,
        @"mobile" : mobile,
        @"sys_type" : @"1"
    };
    
    __block MBProgressHUD *hud = [Utils createLoadingHUDWithTitle:@"正在发送..."];
    __weak typeof(self) weakSelf = self;
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        [hud removeFromSuperview];
        [hud hideAnimated:NO];
        
        NSString *code = [responseObject objectForKey:@"code"];
        
        if ([code isEqualToString:@"0000"]) {
            
            [[[UIApplication sharedApplication] delegate].window makeToast:@"发送成功" duration:kToastDuration position:CSToastPositionCenter];
            
            //发送成功
            [self.navigationController popToRootViewControllerAnimated:YES];
            if (_sendSuccessBlock) {
                _sendSuccessBlock();
            }
            
            
        }else {
            NSString *errorMsgStr = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"errorMsg"]];
            [weakSelf.view makeToast:errorMsgStr duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud removeFromSuperview];
        [hud hideAnimated:NO];
        [self.view makeToast:@"请求失败，请稍后再试" duration:2 position:CSToastPositionCenter];
    }];
    
}

#pragma mark - 分享
- (void)shareContentWithShareUrl:(NSString *)shareUrl preId:(NSString *)preId {
    
    NSString *docName = [[UserManager shareInstance] getUserInfoByKey:kUserDefaultName];
    NSString *titleString = [NSString stringWithFormat:@"%@大夫已为你开好药方!",docName];
    
    NSString *takerName = [self.orderDict objectForKey:@"takerName"];
    NSString *content = [NSString stringWithFormat:@"%@大夫给%@的药方，订单号：%@",docName,takerName,preId];
    
    UIImage *thumbImage = [UIImage imageNamed:@"logo_icon"];
    
    WXWebpageObject *webpageObject = [WXWebpageObject object];
    webpageObject.webpageUrl = shareUrl;
    WXMediaMessage *message = [WXMediaMessage message];
    message.title = titleString;
//            message.description = [NSString stringWithFormat:@"邀请码：%@\n必然中医下载地址：%@",self.qrCodeField.text,kDownloadAPPUrl];
    message.description = content;
    [message setThumbImage:thumbImage];
    message.mediaObject = webpageObject;
    SendMessageToWXReq *req = [[SendMessageToWXReq alloc] init];
    req.bText = NO;
    req.message = message;
    req.scene = WXSceneSession;
    
    __weak __typeof(self)weakSelf = self;
    [WXApi sendReq:req completion:^(BOOL success) {
        if (success) {
            NSLog(@"分享成功===");
            
            [weakSelf.navigationController popToRootViewControllerAnimated:YES];
        }
        else{
            NSLog(@"分享失败===");
            
            [weakSelf.view makeToast:@"分享失败" duration:2 position:CSToastPositionCenter];
        }
    }];
    
}

#pragma mark - 修改剂数按钮
- (void)refreshBtnClick {
    
    [self addLockPreId:_orderId tag:200];
}
#pragma mark - 复制药方 -> 添加药材 按钮
- (void)copyPrescriptionBtnClick {
    
    NSLog(@"details array = %@",self.detailsArray);
    
    BOOL rel = [Config copyPrescriptionWithArray:self.detailsArray];
    
    if (rel) {
        [self.view makeToast:@"复制成功" duration:kToastDuration position:CSToastPositionCenter];
    }
    else {
        [self.view makeToast:@"复制失败" duration:kToastDuration position:CSToastPositionCenter];
    }
}
#pragma mark - 作废按钮
- (void)removeBtnClick {

    [self addLockPreId:_orderId tag:100];
    
}

#pragma mark - 作废
- (void)becomeInvalidPreId:(NSString *)preId
{
    NSDictionary *dict = @{@"method_code"   :@"000065",
                           @"preId"         :preId
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            
            [self.view makeToast:@"订单已作废" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                
                if (self.returnTextBlock != nil) {
                    self.returnTextBlock(1);
                }
                [self.navigationController popViewControllerAnimated:YES];
            }];
        }else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
        [hud hideAnimated:YES];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
    }];
}
#pragma mark - 加锁
- (void)addLockPreId:(NSString *)preId tag:(NSInteger)tag
{
    NSDictionary *dict = @{@"method_code"   :@"000063",
                           @"preId"         :preId
                           };
    MBProgressHUD *hud = [Utils createLoadingHUD];
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            if (tag == 100) {
                
                BRAlertView *alertView = [[BRAlertView alloc] init];
                alertView.isHideWhenTapBackground = YES;
                [alertView.okButton setTitle:@"作废" forState:UIControlStateNormal];
                [alertView showAlertViewWithCancelButton:@"是否作废此药方订单？" completion:^(BOOL isOk) {
                    
                    if (isOk) {
                        [alertView close];
                        [self becomeInvalidPreId:_orderId];
                    }
                    
                }];
                
            } else {
                [self modifyNumber];
            }
            
        } else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
        [hud hideAnimated:YES];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
    }];
}
#pragma mark - 解锁
- (void)unlockPreId:(NSString *)preId
{
    NSDictionary *dict = @{@"method_code"   :@"000064",
                           @"preId"         :preId
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            [self requestData];
        }else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        

    } failure:^(NSURLSessionDataTask *task, NSError *error) {

    }];
}
- (void)modifyNumber
{
    _backview = [[UIView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight)];
    _backview.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];;
    [[UIApplication sharedApplication].keyWindow addSubview:_backview];
    
    _teamView = [[UIView alloc]initWithFrame:CGRectMake(30, (kScreenHeight-300)/2, kScreenWidth - 60, 250)];
    _teamView.backgroundColor = [UIColor whiteColor];
    _teamView.layer.cornerRadius = 5;
    [_backview addSubview:_teamView];
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 30, _teamView.frame.size.width, 18)];
    titleLabel.text = @"修改剂数";
    titleLabel.textColor = [UIColor br_textBlueColor];
    titleLabel.font = kFontLight(17);
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [_teamView addSubview:titleLabel];
    
    _numberTF = [[UITextField alloc]initWithFrame:CGRectMake(30, 84, _teamView.frame.size.width-60,40)];
    _numberTF.layer.cornerRadius = 21;
    _numberTF.placeholder = @"请输入待修改剂数";
    _numberTF.font = kFontLight(16);
    _numberTF.backgroundColor = [UIColor br_backgroundColor];
    _numberTF.leftView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, 15, 0)];
    _numberTF.leftViewMode = UITextFieldViewModeAlways;
    _numberTF.keyboardType = UIKeyboardTypeNumberPad;
    [_teamView addSubview:_numberTF];
    
    
    UIButton *cancelButton = [[UIButton alloc]initWithFrame:CGRectMake(30, _teamView.frame.size.height-76, (_teamView.frame.size.width-75)/2, 40)];
    [cancelButton setTitle:@"取消" forState:UIControlStateNormal];
    [cancelButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [cancelButton addTarget:self action:@selector(senderClick:) forControlEvents:UIControlEventTouchUpInside];
    cancelButton.tag = 200;
    cancelButton.layer.borderColor = [UIColor br_disableBgColor].CGColor;
    cancelButton.layer.borderWidth = 0.5;
    cancelButton.layer.cornerRadius = 5;
    cancelButton.clipsToBounds = YES;
    cancelButton.titleLabel.font = kFontLight(16);
    [_teamView addSubview:cancelButton];
    
    UIButton *saveButton = [[UIButton alloc]initWithFrame:CGRectMake(_teamView.frame.size.width/2+15, _teamView.frame.size.height-76, (_teamView.frame.size.width-75)/2, 40)];
    [saveButton setTitle:@"保存" forState:UIControlStateNormal];
    [saveButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [saveButton setBackgroundImage:[UIImage imageWithColor:[UIColor br_textBlueColor]] forState:UIControlStateNormal];
    [saveButton addTarget:self action:@selector(senderClick:) forControlEvents:UIControlEventTouchUpInside];
    saveButton.tag = 201;
    saveButton.layer.cornerRadius = 5;
    saveButton.clipsToBounds = YES;
    saveButton.titleLabel.font = kFontLight(16);
    [_teamView addSubview:saveButton];
    
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(closeModifyView)];
    [_backview addGestureRecognizer:tapGesture];
}

- (void)closeModifyView {
    [_numberTF resignFirstResponder];
    _backview.hidden = YES;
}

- (void)senderClick:(UIButton *)sender
{
    if (sender.tag == 200) {
        [_numberTF resignFirstResponder];
        _backview.hidden = YES;
    } else {
        NSInteger num = [_numberTF.text integerValue];
        if ([_numberTF.text isEqualToString:@" "] || num == 0 || _numberTF.text.length == 0) {
            
            [_teamView makeToast:@"请输入正确剂数" duration:2 position:CSToastPositionCenter];
            return;
        }
        
        [self modifyNumberPreId:_orderId number:_numberTF.text];
        
    }
}

#pragma mark - 修改付数
- (void)modifyNumberPreId:(NSString *)preId number:(NSString *)number
{
    NSDictionary *dict = @{@"method_code"   :@"000229",
                           @"orderId"       :preId,
                           @"count"         :number,
                           @"apiVer"        : @"4"
                           };
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            
            [_backview removeFromSuperview];
            [self.view makeToast:@"修改成功" duration:2 position:CSToastPositionCenter];
            [self unlockPreId:_orderId];
        } else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
    }];
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField
{
    [textField resignFirstResponder];
    return YES;
}

- (void)returnText:(ReturnTextBlock)block {
    self.returnTextBlock = block;
}

#pragma mark - DZNEmptyDataSetSource

- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [UIImage imageNamed:@"order_canceled"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"订单已失效";
    
    NSDictionary *attributes = @{NSFontAttributeName : [UIFont systemFontOfSize:14],
                                 NSForegroundColorAttributeName : [UIColor lightGrayColor]
                                 };
    
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

- (CGFloat)verticalOffsetForEmptyDataSet:(UIScrollView *)scrollView {
    return -70;
}

#pragma mark 获取订单状态
- (OrderStatus)getOrderStatusWithOrderStatusStr:(NSString *)orderStatusStr {
    
    if (orderStatusStr.length) {
        
        if ([orderStatusStr isEqualToString:@"WAIT_FOR_CONVERSION"]) {
            return OrderStatusWaitForConverson;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_REVIEW"]) {
            return OrderStatusWaitForReview;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_SEND"]) {
            return OrderStatusWaitForSend;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_PAY"]) {
            return OrderStatusWaitForPay;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_ASSIGN_DC"]) {
            return OrderStatusWaitForAssignDC;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_COLLECT"]) {
            return OrderStatusWaitForCollect;
        }
        else if ([orderStatusStr isEqualToString:@"COLLECTING"]) {
            return OrderStatusCollecting;
        }
        else if ([orderStatusStr isEqualToString:@"WAIT_FOR_DELIERY"]) {
            return OrderStatusWaitForDeliery;
        }
        else if ([orderStatusStr isEqualToString:@"ALREADY_DELIVERIED"]) {
            return OrderStatusAlreadyDeliveried;
        }
        else if ([orderStatusStr isEqualToString:@"ALREADY_ARRIVED"]) {
            return OrderStatusAlreadyArrived;
        }
        else if ([orderStatusStr isEqualToString:@"FINIHSED"]) {
            return OrderStatusFinished;
        }
        else if ([orderStatusStr isEqualToString:@"CANCELED"]) {
            return OrderStatusCanceled;
        }
        else if ([orderStatusStr isEqualToString:@"ROLLBACK"]) {
            return OrderStatusRollback;
        }
        else if ([orderStatusStr isEqualToString:@"EXPIRE"]) {
            return OrderStatusExpire;
        }
        else {
            return OrderStatusExpire;
        }
        
    }
    else {
        return OrderStatusExpire;
    }
    
}

#pragma mark 下载图片
- (void)downloadImgWithImgUrl:(NSString *)imgUrl {
    
    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:imgUrl] options:SDWebImageDownloaderHighPriority progress:^(NSInteger receivedSize, NSInteger expectedSize) {
        
    } completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
        //将下载图片缓存到本地磁盘
        [[SDImageCache sharedImageCache] storeImage:image forKey:imgUrl toDisk:YES];
        
        _presPhoto = image;
        
        [self performSelectorOnMainThread:@selector(reloadTableView) withObject:nil waitUntilDone:NO];
        
    }];
    
}

- (void)reloadTableView {
    [_tableView reloadData];
}

- (void)getPhotoBrowser {
    
    if (_browser) {
        [_browser removeFromParentViewController];
    }
    
    _browser = [[MWPhotoBrowser alloc] initWithDelegate:self];
    
    _browser.displayActionButton = NO;
    _browser.displayNavArrows = NO;
    _browser.displaySelectionButtons = NO;
    _browser.zoomPhotosToFill = YES;
    _browser.alwaysShowControls = NO;
    _browser.enableGrid = NO;
    _browser.startOnGrid = NO;
    
    [self presentViewController:_browser animated:YES completion:nil];
    
    [_browser showNextPhotoAnimated:NO];
    [_browser showPreviousPhotoAnimated:NO];
    [_browser setCurrentPhotoIndex:0];
    
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapPhotoGesture)];
    [_browser.view addGestureRecognizer:tapGesture];
    
}

- (void)tapPhotoGesture {
    [_browser dismissViewControllerAnimated:YES completion:nil];
}

- (void)photoBrowser:(MWPhotoBrowser *)photoBrowser didDisplayPhotoAtIndex:(NSUInteger)index {
    
}

- (NSUInteger)numberOfPhotosInPhotoBrowser:(MWPhotoBrowser *)photoBrowser {
    return 1;
}

- (id <MWPhoto>)photoBrowser:(MWPhotoBrowser *)photoBrowser photoAtIndex:(NSUInteger)index {
    return [MWPhoto photoWithImage:_presPhoto];
}

- (void)photoBrowser:(MWPhotoBrowser *)photoBrowser actionButtonPressedForPhotoAtIndex:(NSUInteger)index {
    NSLog(@"index");
}

#pragma mark - 复制订单编号
- (void)copyOrderIdAction:(UIButton *)sender {
    if (self.dataDic && ![self dataIsNull:[self.dataDic objectForKey:@"id"]]) {
        NSString *orderId = [self.dataDic objectForKey:@"id"];
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = orderId;
        [self.view makeToast:@"订单编号已复制到粘贴板" duration:2 position:CSToastPositionCenter];
    }
}

@end

