//
//  WithdrawPasswordViewController.m
//  BRZY
//
//  Created by <PERSON> on 2024/12/19.
//  Copyright © 2024年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "WithdrawPasswordViewController.h"
#import "Reachability.h"
#import "BRAlertView.h"

@interface WithdrawPasswordViewController ()<UIScrollViewDelegate,UITextFieldDelegate>
{
    int         passTime; //用来计时
    int         verTime; //用来计时
}

// 顶部切换按钮
@property (nonatomic, strong) UIButton *passwordButton;//密码按钮
@property (nonatomic, strong) UIButton *verificationButton;//验证码按钮
@property (nonatomic, strong) UIImageView *lineImageView;
@property (nonatomic, strong) UIScrollView *withdrawScrollView;

// 密码方式
@property (nonatomic, strong) UIView *passwordView;
@property (nonatomic, strong) UITextField *oldPasswordTF;
@property (nonatomic, strong) UITextField *passwordTF;
@property (nonatomic, strong) UITextField *confirmPasswordTF;
@property (nonatomic, strong) UIButton *passwordSubmitBtn;

// 验证码方式
@property (nonatomic, strong) UIView *verificationView;
@property (strong, nonatomic) UITextField *mobileTF;
@property (strong, nonatomic) UITextField *captchaTF;
@property (strong, nonatomic) UIButton *getAuthCodeBtn;
@property (strong, nonatomic) UITextField *setPassTF;
@property (strong, nonatomic) UITextField *repeatPassTF;
@property (strong, nonatomic) UIButton *submitBtn;

@property (nonatomic, strong) NSArray *iconArray;
@property (nonatomic, strong) Reachability *reachability;
@property (nonatomic, assign) int time; //用来计时

// 是否已设置提现密码
@property (nonatomic, assign) BOOL hasCashPassword;

@end

@implementation WithdrawPasswordViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBar.hidden = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"修改提现密码";
    [self showNavBackItem];
    
    self.view.backgroundColor = [UIColor whiteColor];
    self.edgesForExtendedLayout = UIRectEdgeNone;
    
    self.time = 60;
    passTime = 60;
    verTime = 60;
    
    // 先检查是否设置过提现密码
    [self checkCashPassword];
}

#pragma mark - 创建顶部切换按钮
- (void)createTopSwitchButtons
{
    _passwordButton = [[UIButton alloc]initWithFrame:CGRectMake((kScreenWidth-220)/2, 0, 110, 45)];
    _passwordButton.tag = 100;
    _passwordButton.selected = YES;
    [_passwordButton setTitle:@"密码方式" forState:UIControlStateNormal];
    [_passwordButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_passwordButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_passwordButton addTarget:self action:@selector(switchButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:_passwordButton];
    
    _verificationButton = [[UIButton alloc]initWithFrame:CGRectMake((kScreenWidth-220)/2+110, 0, 110, 45)];
    _verificationButton.tag = 101;
    _verificationButton.selected = NO;
    [_verificationButton setTitle:@"验证码方式" forState:UIControlStateNormal];
    [_verificationButton setTitleColor:[UIColor br_textBlueColor] forState:UIControlStateSelected];
    [_verificationButton setTitleColor:[UIColor br_textBlackColor] forState:UIControlStateNormal];
    [_verificationButton addTarget:self action:@selector(switchButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:_verificationButton];
    
    _lineImageView = [[UIImageView alloc]initWithFrame:CGRectMake((kScreenWidth-220)/2+20, 42, 70, 3)];
    _lineImageView.backgroundColor = [UIColor br_textBlueColor];
    _lineImageView.layer.cornerRadius = 2;
    [self.view addSubview:_lineImageView];
    
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 45, kScreenWidth, 0.7)];
    imageView.backgroundColor = [UIColor br_insideDivisionLineColor];
    [self.view addSubview:imageView];
}

#pragma mark - 创建滚动视图
- (void)createScrollView
{
    _withdrawScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 45, kScreenWidth, kScreenHeight-64)];
    _withdrawScrollView.contentSize = CGSizeMake(kScreenWidth*2, 0);
    _withdrawScrollView.pagingEnabled = YES;
    _withdrawScrollView.backgroundColor = [UIColor clearColor];
    _withdrawScrollView.delegate = self;
    _withdrawScrollView.contentOffset = CGPointMake(0, 0); // 默认显示密码方式
    [self.view addSubview:_withdrawScrollView];
}

#pragma mark - 创建密码方式界面
- (void)createPasswordView
{
    self.passwordView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, self.withdrawScrollView.frame.size.height)];
    self.passwordView.backgroundColor = [UIColor whiteColor];
    [self.withdrawScrollView addSubview:self.passwordView];
    
    NSArray *titleArray = @[@"原提现密码", @"新提现密码", @"确认新密码"];
    NSArray *placeholderArray = @[@"请输入原提现密码", @"请输入新提现密码", @"请再次输入新密码"];
    
    for (int i = 0; i < 3; i++) {
        UIView *itemView = [[UIView alloc] initWithFrame:CGRectMake(0, 20 + i * 60, kScreenWidth, 60)];
        itemView.backgroundColor = [UIColor whiteColor];
        [self.passwordView addSubview:itemView];
        
        UIImageView *iconImageView = [[UIImageView alloc] initWithFrame:CGRectMake(15, 20, 20, 20)];
        iconImageView.image = [UIImage imageNamed:@"lock_icon"];
        [itemView addSubview:iconImageView];
        
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(45, 0, 100, 60)];
        titleLabel.text = titleArray[i];
        titleLabel.font = kFontLight(16);
        titleLabel.textColor = [UIColor br_textBlackColor];
        [itemView addSubview:titleLabel];
        
        UITextField *textField = [[UITextField alloc] initWithFrame:CGRectMake(145, 0, kScreenWidth - 160, 60)];
        textField.placeholder = placeholderArray[i];
        textField.font = kFontLight(16);
        textField.textColor = [UIColor br_textBlackColor];
        textField.secureTextEntry = YES;
        textField.keyboardType = UIKeyboardTypeNumberPad;
        textField.delegate = self;
        textField.tag = 200 + i; // 200:原密码, 201:新密码, 202:确认密码
        [itemView addSubview:textField];
        
        // 保存textField引用
        if (i == 0) {
            self.oldPasswordTF = textField;
        } else if (i == 1) {
            self.passwordTF = textField;
        } else if (i == 2) {
            self.confirmPasswordTF = textField;
        }
        
        // 添加分割线
        if (i < 2) {
            UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(15, 59, kScreenWidth - 15, 0.7)];
            lineView.backgroundColor = [UIColor br_insideDivisionLineColor];
            [itemView addSubview:lineView];
        }
    }
    
    // 确认修改按钮
    self.passwordSubmitBtn = [[UIButton alloc] initWithFrame:CGRectMake(15, 20 + 3 * 60 + 40, kScreenWidth - 30, 44)];
    self.passwordSubmitBtn.backgroundColor = [UIColor br_textBlueColor];
    [self.passwordSubmitBtn setTitle:@"确认修改" forState:UIControlStateNormal];
    [self.passwordSubmitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.passwordSubmitBtn.titleLabel.font = kFontLight(16);
    self.passwordSubmitBtn.layer.cornerRadius = 22;
    self.passwordSubmitBtn.layer.masksToBounds = YES;
    [self.passwordSubmitBtn addTarget:self action:@selector(passwordSubmitAction) forControlEvents:UIControlEventTouchUpInside];
    [self.passwordView addSubview:self.passwordSubmitBtn];
}

#pragma mark - 创建验证码方式界面
- (void)createVerificationCodeWayInterface
{
    _verificationView = [[UIView alloc] initWithFrame:CGRectMake(kScreenWidth, 0, kScreenWidth, _withdrawScrollView.frame.size.height)];
    _verificationView.backgroundColor = [UIColor clearColor];
    [_withdrawScrollView addSubview:_verificationView];
    
    _iconArray = @[@"registered_tel",@"registered_verificationCode",@"lock_icon",@"lock_icon"];
    
    for (int i = 0; i < 4; i++) {
        UIImageView *lineImage = [[UIImageView alloc] initWithFrame:CGRectMake(30, 52+52*i, kScreenWidth-60, 0.7)];
        lineImage.backgroundColor = [UIColor colorWithHex:0xcccccc];
        [_verificationView addSubview:lineImage];
        
        UIImageView *iconImage = [[UIImageView alloc] initWithFrame:CGRectMake(30, 25+52*i, 20, 20)];
        iconImage.image = [UIImage imageNamed:[_iconArray objectAtIndex:i]];
        [_verificationView addSubview:iconImage];
    }
    
    _mobileTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 20, kScreenWidth-90, 32)];
    _mobileTF.keyboardType = UIKeyboardTypeNumberPad;
    _mobileTF.text = [UserManager shareInstance].getTelephone;
    _mobileTF.enabled = NO;
    _mobileTF.backgroundColor = [UIColor clearColor];
    _mobileTF.font = kFontLight(16);
    _mobileTF.delegate = self;
    [_verificationView addSubview:_mobileTF];
    
    _captchaTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 72, kScreenWidth-200, 32)];
    _captchaTF.placeholder = @"短信验证码";
    _captchaTF.font = kFontLight(16);
    _captchaTF.keyboardType = UIKeyboardTypeNumberPad;
    _captchaTF.delegate = self;
    [_verificationView addSubview:_captchaTF];
    
    _getAuthCodeBtn = [[UIButton alloc] initWithFrame:CGRectMake(kScreenWidth-150, 67, 120, 32)];
    _getAuthCodeBtn.backgroundColor = [UIColor br_textBlueColor];
    _getAuthCodeBtn.layer.cornerRadius = 16;
    _getAuthCodeBtn.clipsToBounds = YES;
    [_getAuthCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
    [_getAuthCodeBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _getAuthCodeBtn.titleLabel.font = kFontLight(14);
    [_getAuthCodeBtn addTarget:self action:@selector(getAuthCodeButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_verificationView addSubview:_getAuthCodeBtn];
    
    _setPassTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 124, kScreenWidth-90, 32)];
    _setPassTF.placeholder = @"设置6位数字提现密码";
    _setPassTF.font = kFontLight(16);
    _setPassTF.secureTextEntry = YES;
    _setPassTF.keyboardType = UIKeyboardTypeNumberPad;
    _setPassTF.delegate = self;
    [_verificationView addSubview:_setPassTF];
    
    _repeatPassTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 176, kScreenWidth-90, 32)];
    _repeatPassTF.placeholder = @"再次输入6位数字密码";
    _repeatPassTF.font = kFontLight(16);
    _repeatPassTF.secureTextEntry = YES;
    _repeatPassTF.keyboardType = UIKeyboardTypeNumberPad;
    _repeatPassTF.delegate = self;
    [_verificationView addSubview:_repeatPassTF];
    
    _submitBtn = [[UIButton alloc] initWithFrame:CGRectMake(30, 250, kScreenWidth-60, 40)];
    [_submitBtn setTitle:@"确认设置" forState:UIControlStateNormal];
    [_submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _submitBtn.titleLabel.font = kFontRegular(18);
    _submitBtn.backgroundColor = [UIColor br_textBlueColor];
    _submitBtn.layer.cornerRadius = 20;
    _submitBtn.clipsToBounds = YES;
    [_submitBtn addTarget:self action:@selector(submitButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_verificationView addSubview:_submitBtn];
}

#pragma mark - 密码验证码方式切换
- (void)switchButtonClick:(UIButton *)button
{
    [self headerViewSegmentedUI:button.tag];
}

- (void)headerViewSegmentedUI:(NSInteger)tag
{
    if (tag == 100) {
        _passwordButton.selected = YES;
        _verificationButton.selected = NO;
        _lineImageView.left = (kScreenWidth-220)/2+20;
        _withdrawScrollView.contentOffset = CGPointMake(0, 0);
    } else if (tag == 101) {
        _passwordButton.selected = NO;
        _verificationButton.selected = YES;
        _lineImageView.left = (kScreenWidth-220)/2+130;
        _withdrawScrollView.contentOffset = CGPointMake(kScreenWidth, 0);
    }
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    if (scrollView == _withdrawScrollView) {
        CGFloat f = scrollView.contentOffset.x;
        if (f == 0) {
            [self headerViewSegmentedUI:100];
        } else if (f == kScreenWidth) {
            [self headerViewSegmentedUI:101];
        }
    }
}

#pragma mark - 密码方式提交
- (void)passwordSubmitAction
{
    if (![self validatePasswordInput]) {
        return;
    }
    
    [self modifyWithdrawPasswordByOldPassword];
}

#pragma mark - 获取验证码
- (void)getAuthCodeButtonClick:(UIButton *)button
{
    //防止重复点击
    button.userInteractionEnabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        button.userInteractionEnabled = YES;
    });
    
    [self requestVerification];
}

#pragma mark - 验证码方式提交
- (void)submitButtonClick:(UIButton *)button
{
    if (![self validateInput]) {
        return;
    }
    
    [self submitWithdrawPassword];
}

#pragma mark - 验证输入（验证码方式）
- (BOOL)validateInput
{
    NSString *newPassword = _setPassTF.text.length ? [_setPassTF.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] : @"";
    NSString *confirmPassword = _repeatPassTF.text.length ? [_repeatPassTF.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] : @"";
    
    if (!self.mobileTF.text.length) {
        [self.view makeToast:@"请输入11位手机号" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (!_captchaTF.text.length) {
        [self.view makeToast:@"请输入短信验证码" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (newPassword.length == 0){
        [self.view makeToast:@"请输入提现密码" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (newPassword.length != 6) {
        [self.view makeToast:@"提现密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if ([newPassword rangeOfCharacterFromSet:[[NSCharacterSet decimalDigitCharacterSet] invertedSet]].location != NSNotFound) {
        [self.view makeToast:@"提现密码只能输入数字" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (confirmPassword.length == 0){
        [self.view makeToast:@"请再次输入提现密码" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (confirmPassword.length != 6) {
        [self.view makeToast:@"确认密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if ([confirmPassword rangeOfCharacterFromSet:[[NSCharacterSet decimalDigitCharacterSet] invertedSet]].location != NSNotFound) {
        [self.view makeToast:@"确认密码只能输入数字" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if (![newPassword isEqualToString:confirmPassword]) {
        [self.view makeToast:@"两次输入的密码不一致" duration:2 position:CSToastPositionCenter];
        return NO;
    } else if ([self isConsecutiveNumbers:newPassword] || [self isRepeatingNumbers:newPassword]) {
        [self.view makeToast:@"密码不能为连续数字或相同数字，请重新设置" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    return YES;
}

#pragma mark - 验证输入（密码方式）
- (BOOL)validatePasswordInput
{
    NSString *oldPassword = self.oldPasswordTF.text.length ? [self.oldPasswordTF.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] : @"";
    NSString *newPassword = self.passwordTF.text.length ? [self.passwordTF.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] : @"";
    NSString *confirmPassword = self.confirmPasswordTF.text.length ? [self.confirmPasswordTF.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] : @"";
    
    // 验证原密码
    if (oldPassword.length == 0) {
        [self.view makeToast:@"请输入原提现密码" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    if (oldPassword.length != 6) {
        [self.view makeToast:@"原提现密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
    if ([oldPassword rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
        [self.view makeToast:@"原密码只能输入数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }

    // 验证新密码
    if (newPassword.length == 0) {
        [self.view makeToast:@"请输入提现密码" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    if (newPassword.length != 6) {
        [self.view makeToast:@"提现密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    if ([newPassword rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
        [self.view makeToast:@"提现密码只能输入数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }

    // 验证确认密码
    if (confirmPassword.length == 0) {
        [self.view makeToast:@"请输入确认密码" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    if (confirmPassword.length != 6) {
        [self.view makeToast:@"确认密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    if ([confirmPassword rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
        [self.view makeToast:@"确认密码只能输入数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }

    // 验证两次密码是否一致
    if (![newPassword isEqualToString:confirmPassword]) {
        [self.view makeToast:@"两次输入的密码不一致" duration:2 position:CSToastPositionCenter];
        return NO;
    }

    // 验证密码强度（不能是连续数字或相同数字）
    if ([self isConsecutiveNumbers:newPassword] || [self isRepeatingNumbers:newPassword]) {
        [self.view makeToast:@"密码不能为连续数字或相同数字，请重新设置" duration:2 position:CSToastPositionCenter];
        return NO;
    }

    return YES;
}

#pragma mark - 验证密码格式
- (BOOL)isValidPassword:(NSString *)password
{
    // 检查密码长度是否为6位
    if (password.length != 6) {
        [self.view makeToast:@"提现密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    // 检查是否全为数字
    NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
    if ([password rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
        [self.view makeToast:@"提现密码只能包含数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    // 检查是否为连续数字
    if ([self isConsecutiveNumbers:password]) {
        [self.view makeToast:@"提现密码不能为连续数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    // 检查是否为重复数字
    if ([self isRepeatingNumbers:password]) {
        [self.view makeToast:@"提现密码不能为重复数字" duration:2 position:CSToastPositionCenter];
        return NO;
    }
    
    return YES;
}

#pragma mark - 检查是否为连续数字
- (BOOL)isConsecutiveNumbers:(NSString *)password
{
    for (int i = 0; i < password.length - 1; i++) {
        int currentDigit = [password characterAtIndex:i] - '0';
        int nextDigit = [password characterAtIndex:i + 1] - '0';
        
        // 检查是否为连续递增或递减
        if (abs(nextDigit - currentDigit) != 1) {
            return NO;
        }
        
        // 如果不是第一个字符，检查连续性方向是否一致
        if (i > 0) {
            int prevDigit = [password characterAtIndex:i - 1] - '0';
            int prevDirection = currentDigit - prevDigit;
            int currentDirection = nextDigit - currentDigit;
            
            if (prevDirection != currentDirection) {
                return NO;
            }
        }
    }
    
    return YES;
}

#pragma mark - 检查是否为重复数字
- (BOOL)isRepeatingNumbers:(NSString *)password
{
    char firstChar = [password characterAtIndex:0];
    
    for (int i = 1; i < password.length; i++) {
        if ([password characterAtIndex:i] != firstChar) {
            return NO;
        }
    }
    
    return YES;
}

#pragma mark - 获取验证码接口
- (void)requestVerification
{
    NSDictionary *dict = @{@"method_code":@"000031",
                           @"type":@"1",
                           @"mobile":_mobileTF.text
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            [self.view makeToast:@"验证码已发送" duration:2 position:CSToastPositionCenter];
            [self startTimer];
        } else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [self.view makeToast:@"网络请求失败" duration:2 position:CSToastPositionCenter];
    }];
}

#pragma mark - 设置提现密码接口
- (void)submitWithdrawPassword
{
    if (self.hasCashPassword) {
        // 已设置提现密码，使用修改提现密码接口（验证码方式）
        NSDictionary *dict = @{@"method_code":@"000456",
                               @"userId":[UserManager shareInstance].getUserId,
                               @"password":[[_setPassTF.text md5String] uppercaseString],
                               @"smsCode":_captchaTF.text,
                               @"oldPassword":@"" // 验证码方式不需要旧密码
                               };
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            NSLog(@"修改提现密码结果: %@", responseObject);
            
            if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
                [self.view makeToast:@"提现密码修改成功" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                    [self.navigationController popViewControllerAnimated:YES];
                }];
            } else {
                [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            [self.view makeToast:@"网络请求失败" duration:2 position:CSToastPositionCenter];
        }];
        
    } else {
        // 首次设置提现密码，使用首次设置提现密码接口
        NSDictionary *dict = @{@"method_code":@"000455",
                               @"userId":[UserManager shareInstance].getUserId,
                               @"password":[[_setPassTF.text md5String] uppercaseString],
                               @"smsCode":_captchaTF.text
                               };
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            
            NSLog(@"首次设置提现密码结果: %@", responseObject);
            
            if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
                [self.view makeToast:@"提现密码设置成功" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                    [self.navigationController popViewControllerAnimated:YES];
                }];
            } else {
                [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
            
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            [self.view makeToast:@"网络请求失败" duration:2 position:CSToastPositionCenter];
        }];
    }
}

#pragma mark - 倒计时
- (void)startTimer
{
    [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:nil];
    NSTimer *timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(timerAdvanced:) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:timer forMode:NSRunLoopCommonModes];
}

- (void)timerAdvanced:(NSTimer *)timer
{
    _time--;
    
    if (_time == 0) {
        [timer invalidate];
        _time = 60;
        
        [_getAuthCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
        _getAuthCodeBtn.backgroundColor = [UIColor br_textBlueColor];
        _getAuthCodeBtn.enabled = YES;
    }
    else {
        [_getAuthCodeBtn setTitle:[NSString stringWithFormat:@"重新发送(%d)",_time] forState:UIControlStateNormal];
        _getAuthCodeBtn.backgroundColor = [UIColor lightGrayColor];
        _getAuthCodeBtn.enabled = NO;
    }
}

#pragma mark - UITextFieldDelegate
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    // 手机号输入限制
    if (textField == _mobileTF) {
        if (string.length == 0)
            return YES;
        
        NSInteger existedLength = textField.text.length;
        NSInteger selectedLength = range.length;
        NSInteger replaceLength = string.length;
        
        if (existedLength - selectedLength + replaceLength > 11) {
            return NO;
        }
    }
    
    // 密码输入限制（6位数字）
    if (textField == _setPassTF || textField == _repeatPassTF || 
        textField == self.passwordTF || textField == self.confirmPasswordTF ||
        textField == self.oldPasswordTF) {
        
        // 只允许数字输入
        NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
        if ([string rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
            return NO;
        }
        
        // 限制长度为6位
        NSString *newText = [textField.text stringByReplacingCharactersInRange:range withString:string];
        return newText.length <= 6;
    }
    
    return YES;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - 检查是否已设置提现密码
- (void)checkCashPassword
{
    // 检查提现密码接口
    NSDictionary *dict = @{@"method_code":@"000454",
                           @"userId":[UserManager shareInstance].getUserId
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            // 获取hasCashPwd字段
            BOOL hasCashPwd = [[responseObject objectForKey:@"hasCashPwd"] boolValue];
            self.hasCashPassword = hasCashPwd;
            
            // 根据是否已设置提现密码来配置界面
            [self setupUIBasedOnCashPasswordStatus];
            
        } else {
            // 如果接口调用失败，默认认为没有设置提现密码
            self.hasCashPassword = NO;
            [self setupUIBasedOnCashPasswordStatus];
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        // 网络请求失败，默认认为没有设置提现密码
        self.hasCashPassword = NO;
        [self setupUIBasedOnCashPasswordStatus];
        [self.view makeToast:@"网络请求失败" duration:2 position:CSToastPositionCenter];
    }];
}

#pragma mark - 根据提现密码状态设置界面
- (void)setupUIBasedOnCashPasswordStatus
{
    if (self.hasCashPassword) {
        // 已设置提现密码，显示修改提现密码界面（包含切换功能）
        self.title = @"修改提现密码";
        
        // 创建顶部切换按钮
        [self createTopSwitchButtons];
        
        // 创建滚动视图
        [self createScrollView];
        
        // 创建密码方式界面
        [self createPasswordView];
        
        // 创建验证码方式界面
        [self createVerificationCodeWayInterface];
        
    } else {
        // 未设置提现密码，显示设置提现密码界面（只显示验证码方式）
        self.title = @"设置提现密码";
        
        // 直接创建验证码方式界面，不需要切换功能
        [self createSetPasswordInterface];
    }
}

#pragma mark - 创建设置提现密码界面（只有验证码方式）
- (void)createSetPasswordInterface
{
    // 创建验证码方式界面，占满整个视图
    _verificationView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight-64)];
    _verificationView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:_verificationView];
    
    // 添加顶部说明文字
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 20, kScreenWidth-30, 50)];
    tipLabel.text = @"为了您的资金安全，请设置提现密码";
    tipLabel.textColor = [UIColor br_textBlackColor];
    tipLabel.font = kFontLight(16);
    tipLabel.textAlignment = NSTextAlignmentCenter;
    tipLabel.numberOfLines = 0;
    [_verificationView addSubview:tipLabel];
    
    _iconArray = @[@"registered_tel",@"registered_verificationCode",@"lock_icon",@"lock_icon"];
    
    // 调整输入框位置，为顶部说明文字留出空间
    for (int i = 0; i < 4; i++) {
        UIImageView *lineImage = [[UIImageView alloc] initWithFrame:CGRectMake(30, 90+52+52*i, kScreenWidth-60, 0.7)];
        lineImage.backgroundColor = [UIColor colorWithHex:0xcccccc];
        [_verificationView addSubview:lineImage];
        
        UIImageView *iconImage = [[UIImageView alloc] initWithFrame:CGRectMake(30, 90+25+52*i, 20, 20)];
        iconImage.image = [UIImage imageNamed:[_iconArray objectAtIndex:i]];
        [_verificationView addSubview:iconImage];
    }
    
    _mobileTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 90+20, kScreenWidth-90, 32)];
    _mobileTF.keyboardType = UIKeyboardTypeNumberPad;
    _mobileTF.text = [UserManager shareInstance].getTelephone;
    _mobileTF.enabled = NO;
    _mobileTF.backgroundColor = [UIColor clearColor];
    _mobileTF.font = kFontLight(16);
    _mobileTF.delegate = self;
    [_verificationView addSubview:_mobileTF];
    
    _captchaTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 90+72, kScreenWidth-200, 32)];
    _captchaTF.placeholder = @"短信验证码";
    _captchaTF.font = kFontLight(16);
    _captchaTF.keyboardType = UIKeyboardTypeNumberPad;
    _captchaTF.delegate = self;
    [_verificationView addSubview:_captchaTF];
    
    _getAuthCodeBtn = [[UIButton alloc] initWithFrame:CGRectMake(kScreenWidth-150, 90+67, 120, 32)];
    _getAuthCodeBtn.backgroundColor = [UIColor br_textBlueColor];
    _getAuthCodeBtn.layer.cornerRadius = 16;
    _getAuthCodeBtn.clipsToBounds = YES;
    [_getAuthCodeBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
    [_getAuthCodeBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _getAuthCodeBtn.titleLabel.font = kFontLight(14);
    [_getAuthCodeBtn addTarget:self action:@selector(getAuthCodeButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_verificationView addSubview:_getAuthCodeBtn];
    
    _setPassTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 90+124, kScreenWidth-90, 32)];
    _setPassTF.placeholder = @"设置6位数字提现密码";
    _setPassTF.font = kFontLight(16);
    _setPassTF.secureTextEntry = YES;
    _setPassTF.keyboardType = UIKeyboardTypeNumberPad;
    _setPassTF.delegate = self;
    [_verificationView addSubview:_setPassTF];
    
    _repeatPassTF = [[UITextField alloc] initWithFrame:CGRectMake(60, 90+176, kScreenWidth-90, 32)];
    _repeatPassTF.placeholder = @"再次输入6位数字密码";
    _repeatPassTF.font = kFontLight(16);
    _repeatPassTF.secureTextEntry = YES;
    _repeatPassTF.keyboardType = UIKeyboardTypeNumberPad;
    _repeatPassTF.delegate = self;
    [_verificationView addSubview:_repeatPassTF];
    
    _submitBtn = [[UIButton alloc] initWithFrame:CGRectMake(30, 90+250, kScreenWidth-60, 40)];
    [_submitBtn setTitle:@"确认设置" forState:UIControlStateNormal];
    [_submitBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    _submitBtn.titleLabel.font = kFontRegular(18);
    _submitBtn.backgroundColor = [UIColor br_textBlueColor];
    _submitBtn.layer.cornerRadius = 20;
    _submitBtn.clipsToBounds = YES;
    [_submitBtn addTarget:self action:@selector(submitButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [_verificationView addSubview:_submitBtn];
}

#pragma mark - 修改提现密码接口（密码方式）
- (void)modifyWithdrawPasswordByOldPassword
{
    NSDictionary *dict = @{@"method_code":@"000456",
                           @"userId":[UserManager shareInstance].getUserId,
                           @"password":[[self.passwordTF.text md5String] uppercaseString],
                           @"smsCode":@"", // 密码方式不需要验证码
                           @"oldPassword":[[self.oldPasswordTF.text md5String] uppercaseString]
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        NSLog(@"修改提现密码结果: %@", responseObject);
        
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            [self.view makeToast:@"提现密码修改成功" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                [self.navigationController popViewControllerAnimated:YES];
            }];
        } else {
            [self.view makeToast:[responseObject objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [self.view makeToast:@"网络请求失败" duration:2 position:CSToastPositionCenter];
    }];
}

@end 
