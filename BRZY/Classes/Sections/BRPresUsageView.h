//
//  BRPresUsageView.h
//  BRZY
//  开处方用法用量
//  Created by e<PERSON><PERSON> on 2017/10/13.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

#import "BRUnderlineRedTextField.h"
#import "BRSubFactoryModel.h"
#import "BRPackageSpecModel.h"

typedef void(^ChangeTimeBlock)(void);
typedef void(^DrugNumChanged)(NSString *);
typedef void(^textFieldEditingDidBegin)(void);
typedef void(^changeDayBlock)(NSString *);
typedef void(^changeUsageHeight)(CGFloat);
typedef void(^ClickOtherTypeMarkBlock)(void);
// 添加新的block类型用于处理代煎剂型下"每剂分几次服用"的点击事件
typedef void(^ServingTimesSelectionBlock)(void);

typedef NS_ENUM(NSInteger, BRDrugType){
    BRDrugTypeKAndY,//颗粒饮片外用中药
    BRDrugTypeOther//其他的膏方一类的
};

@interface BRPresUsageView : UIView

@property (nonatomic, copy) ChangeTimeBlock changeTime;
@property (nonatomic, strong) UILabel *totalDoseLabel;
@property (nonatomic, strong) BRUnderlineRedTextField *drugNumTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *usageTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *timesTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *preDoseTextField;
@property (nonatomic, strong) BRUnderlineRedTextField *dayTextField;
@property (nonatomic, strong) UIButton *changeTimeButton;
@property (nonatomic, copy) DrugNumChanged drugChanged;
@property (nonatomic, copy) textFieldEditingDidBegin textFieldEditingDidBegin;
// 胶囊克数输入回调已移除

// 添加新的属性用于处理代煎剂型下"每剂分几次服用"的点击事件
@property (nonatomic, copy) ServingTimesSelectionBlock servingTimesSelectionBlock;

// 规格选择回调
typedef void(^SpecificationSelectionBlock)(void);
@property (nonatomic, copy) SpecificationSelectionBlock specificationSelectionBlock;

// 胶囊颗粒数显示相关属性已移除


@property (nonatomic, copy) changeDayBlock changeDay;
@property (nonatomic, copy) ClickOtherTypeMarkBlock clickOtherTypeBlock;
@property (nonatomic, copy) changeUsageHeight changeUsageHeight;

//蜜丸规格
@property (nonatomic, strong) UIButton *changeHoneyPillSpecificationButton;

@property (nonatomic, strong) NSString *drugForm;
- (void)setDrugForm:(NSString *)drugForm;
@property (nonatomic, assign) BRDrugType drugType;

@property (nonatomic, strong) NSString *totalDoseStr;
- (void)setTotalDoseStr:(NSString *)totalDoseStr;

@property (nonatomic, copy) NSString *patientId;
//快速开方时间戳 临时id
@property (nonatomic, copy) NSString *quickOrderTempId;


// 新增用药方法选择按钮
@property (nonatomic, strong) UIButton *internalUseButton;  // 内服按钮
@property (nonatomic, strong) UIButton *externalUseButton;  // 外用按钮
@property (nonatomic, strong) UILabel *usageMethodLabel;    // "用药方法"标签
@property (nonatomic, copy) NSString *selectedMode;         // 选中的用药方法

// 行容器视图数组
@property (nonatomic, strong) NSMutableArray<UIView *> *rowContainers; // 所有行的容器视图

// 规格行相关
@property (nonatomic, strong) UILabel *specificationLabel; // 规格标签
@property (nonatomic, strong) UIView *specificationSelectorView; // 规格选择框容器
@property (nonatomic, strong) UILabel *specificationPlaceholderLabel; // 规格选择框占位文字
@property (nonatomic, strong) UIImageView *specificationArrowImageView; // 规格选择框下拉箭头

// 规格行控制方法
- (void)setSpecificationRowHidden:(BOOL)hidden;

// 规格数据管理
@property (nonatomic, strong) NSArray<BRPackageSpecModel *> *packageSpecList; // 规格数据列表
@property (nonatomic, strong) NSString *selectedSpecification; // 当前选中的规格
@property (nonatomic, strong) BRSubFactoryModel *currentFactoryModel; // 当前厂商模型

// 克数显示标签（显示计算后的克数，如 (6g)）
@property (nonatomic, strong) UILabel *preDoseGramLabel;

// 用量单位标签（显示包装单位，如"丸"、"袋"、"粒"等）
@property (nonatomic, strong) UILabel *preDoseUnitLabel;

// 规格解析相关属性
// 蜜丸剂型规格解析
@property (nonatomic, copy) NSString *currentSpecUnit; // 当前规格的包装单位（如"丸"）
@property (nonatomic, assign) double currentSpecGramPerUnit; // 当前规格每单位的重量数值（如3.0）
@property (nonatomic, copy) NSString *currentSpecWeightUnit; // 当前规格的重量单位（如"g"、"mg"、"ml"）
@property (nonatomic, assign) double selectedUnitCount; // 用户选择的单位数量（支持小数，如1、2、3或1.5、2.5等）

// 胶囊剂型规格解析
@property (nonatomic, copy) NSString *currentCapsuleSpecUnit; // 当前胶囊规格的包装单位（如"粒"）
@property (nonatomic, assign) double currentCapsuleSpecGramPerUnit; // 当前胶囊规格每单位的重量数值（如0.5）
@property (nonatomic, copy) NSString *currentCapsuleSpecWeightUnit; // 当前胶囊规格的重量单位（如"g"、"mg"）
@property (nonatomic, assign) NSInteger selectedCapsuleUnitCount; // 用户选择的胶囊单位数量（3、6、9）

// 水丸剂型规格解析
@property (nonatomic, copy) NSString *currentWaterPillSpecUnit; // 当前水丸规格的包装单位（如"丸"）
@property (nonatomic, assign) double currentWaterPillSpecGramPerUnit; // 当前水丸规格每单位的重量数值（如3.0）
@property (nonatomic, copy) NSString *currentWaterPillSpecWeightUnit; // 当前水丸规格的重量单位（如"g"、"mg"）
@property (nonatomic, assign) double selectedWaterPillUnitCount; // 用户选择的水丸单位数量（支持小数，如1、2、3或1.5、2.5等）

// 膏方剂型规格解析
@property (nonatomic, copy) NSString *currentCreamFormulaSpecUnit; // 当前膏方规格的包装单位（如"勺"）
@property (nonatomic, assign) double currentCreamFormulaSpecGramPerUnit; // 当前膏方规格每单位的重量数值（如10.0）
@property (nonatomic, copy) NSString *currentCreamFormulaSpecWeightUnit; // 当前膏方规格的重量单位（如"g"、"ml"）
@property (nonatomic, assign) double selectedCreamFormulaUnitCount; // 用户选择的膏方单位数量（支持小数，如1、2、3或1.5、2.5等）
@property (nonatomic, assign) BOOL isCreamFormulaBottlePackage; // 是否为瓶装膏方（用于特殊处理）
@property (nonatomic, assign) double creamFormulaDirectWeightValue; // 瓶装膏方直接输入的重量数值（如15.0表示15g或15ml）

// 规格业务逻辑方法
- (void)updateSpecificationWithFactoryModel:(BRSubFactoryModel *)factoryModel;
- (void)initSpecificationData;
- (BOOL)shouldShowSpecificationRowForDrugForm:(NSString *)drugForm;
- (void)updateSpecificationDisplayText:(NSString *)specText;

// 规格解析方法
- (void)parseSpecificationData:(NSString *)specValue;
- (void)parseCapsuleSpecificationData:(NSString *)specValue;
- (void)parseWaterPillSpecificationData:(NSString *)specValue;
- (void)parseCreamFormulaSpecificationData:(NSString *)specValue;

// 通用规格解析方法（根据当前剂型自动选择）
- (void)parseSpecificationForCurrentDrugForm:(NSString *)specValue;

// 用量显示更新方法
- (void)updatePillDosageDisplay;
- (void)updateCapsuleDosageDisplay;
- (void)updateWaterPillDosageDisplay;
- (void)updateCreamFormulaDosageDisplay;

// 克数显示更新方法
- (void)updatePillGramDisplay;
- (void)updateCapsuleGramDisplay;
- (void)updateWaterPillGramDisplay;
- (void)updateCreamFormulaGramDisplay;

// 数量选择弹窗方法
- (void)showPillUnitCountSelection;
- (void)showCapsuleUnitCountSelection;
- (void)showWaterPillUnitCountSelection;
- (void)showCreamFormulaUnitCountSelection;

// 自定义输入弹窗方法
- (void)showCustomInputDialog;

// 辅料相关属性和方法
@property (nonatomic, strong) UILabel *auxiliaryMaterialLabel; // 辅料标签
@property (nonatomic, strong) UIView *auxiliaryMaterialSelectorView; // 辅料选择框容器
@property (nonatomic, strong) UILabel *auxiliaryMaterialPlaceholderLabel; // 辅料选择框占位文字
@property (nonatomic, strong) UIImageView *auxiliaryMaterialArrowImageView; // 辅料选择框下拉箭头
@property (nonatomic, strong) NSArray<NSString *> *auxiliaryMaterialList; // 辅料数据列表
@property (nonatomic, strong) NSMutableArray<NSString *> *selectedAuxiliaryMaterials; // 已选择的辅料列表
@property (nonatomic, assign) BOOL isNoAuxiliaryMaterial; // 是否选择"不添加辅料"

// 辅料选择回调
typedef void(^AuxiliaryMaterialSelectionBlock)(void);
@property (nonatomic, copy) AuxiliaryMaterialSelectionBlock auxiliaryMaterialSelectionBlock;

// 辅料相关方法
- (void)setAuxiliaryMaterialRowHidden:(BOOL)hidden;
- (void)updateAuxiliaryMaterialDisplayText:(NSString *)displayText;
- (BOOL)shouldShowAuxiliaryMaterialRowForDrugForm:(NSString *)drugForm;
- (void)updateAuxiliaryMaterialWithFactoryModel:(BRSubFactoryModel *)factoryModel;

@end
