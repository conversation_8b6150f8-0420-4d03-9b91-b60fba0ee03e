//
//  SocketManager.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON>ta<PERSON> on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "SocketManager.h"
#import "GCDAsyncSocket.h"

#import "BRLoginModel.h"
#import "BRMessagesModel.h"
#import "BRSessionListModel.h"

static SocketManager *socketManager = nil;
static NSInteger kMaxReconnectionTime = 5; //最大重连次数
static NSInteger kPingTime = 14;

@interface SocketManager ()<GCDAsyncSocketDelegate>

@property (strong, nonatomic) NSMutableSet *delegateSet;
@property (strong, nonatomic) GCDAsyncSocket *socket;
@property (nonatomic) NSInteger status;         //连接状态 -1 为未连接
@property (nonatomic) NSInteger reconnectionTime;
@property (strong, nonatomic) NSTimer *timer;

@property (assign, nonatomic) NSInteger connectCount;//尝试连接次数

@property (strong, nonatomic) NSTimer *messageReSendTimer;
@property (strong, nonatomic) YYThreadSafeArray *waitSendMessagesArray;
@property (nonatomic) NSInteger messageReSendCount;

@end

@implementation SocketManager

+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        socketManager = [[SocketManager alloc] init];
    });
    return socketManager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _messageReSendCount = 0;
        _waitSendMessagesArray = [YYThreadSafeArray arrayWithCapacity:0];
    }
    return self;
}

- (NSMutableSet *)delegateSet {
    if (!_delegateSet) {
        _delegateSet = [NSMutableSet new];
    }
    return _delegateSet;
}

- (void)connectToHost {
    self.socket = [[GCDAsyncSocket alloc] initWithDelegate:self delegateQueue:dispatch_get_main_queue()];
    self.socket.delegate = self;
    NSError *error = nil;
    BOOL rel = [self.socket connectToHost:kSocketAddress onPort:kSocketSSLPort withTimeout:-1 error:&error];
    
    if (rel) {
        [self.socket startTLS:nil];
    }
    
    if (self.messageReSendTimer == nil) {
        self.messageReSendTimer = [NSTimer scheduledTimerWithTimeInterval:2 target:self selector:@selector(reSendMessage) userInfo:nil repeats:YES];
    }
}

- (void)reConnection {
    [self connectToHost];
}
#pragma mark - 发送消息
- (void)sendDataDict:(NSDictionary *)dict {
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionaryWithDictionary:dict];
    [dictionary setObject:[UserManager shareInstance].getUserId forKey:@"userid"];
    [dictionary setObject:@"biran" forKey:@"terminalType"];
    
    NSString *code = [dictionary objectForKey:@"code"];
    //发送的消息 0008
    if ([code isEqualToString:BRApiMessage]) {
        [_waitSendMessagesArray addObject:dictionary];
    }
    else{
        NSString *jsonString = [[dictionary mj_JSONString] stringByAppendingString:@"\n"];
        if ([code isEqualToString:@"0002"]) {
            NSLog(@"0002 login json string = %@",jsonString);
        }
        
        [self.socket writeData:[jsonString dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:-1];
    }
}

- (NSString *)sendMessage {
    if (_waitSendMessagesArray.count > 0) {
        NSDictionary *dict = [_waitSendMessagesArray objectAtIndex:0];
        
        NSString *jsonString = [[dict mj_JSONString] stringByAppendingString:@"\n"];
        [self.socket writeData:[jsonString dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:-1];
        
        return [[[dict objectForKey:@"messages"] objectAtIndex:0] objectForKey:@"fromId"];
    }
    else {
        return nil;
    }
}

- (void)messageSendSuccessful:(BRMessagesModel *)messagesModel {
    if ((messagesModel.messages.count > 0) && (_waitSendMessagesArray.count > 0)) {
        //接收到消息回执后  删除数组中第一个 开始重新发送消息
        [_waitSendMessagesArray removeObjectAtIndex:0];
        self.messageReSendCount = 0;
        [self sendMessage];
    }
}

- (void)reSendMessage {
    NSString *fromId = [self sendMessage];
    if (fromId == nil) {
        return;
    }
    self.messageReSendCount++;
    
    if (self.messageReSendCount > 15) {
        //发送失败
        for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(socketManagerDidSDKSendMessageFailed:)]) {
                [delegate socketManagerDidSDKSendMessageFailed:fromId];
            }
        }
        //发送下一条
        if (_waitSendMessagesArray.count > 0) {
            [_waitSendMessagesArray removeObjectAtIndex:0];
            self.messageReSendCount = 0;
            [self sendMessage];
        }
    }
}

- (void)sendHeartBeat {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict setObject:@"0007" forKey:@"code"];
    [dict setObject:@"ping" forKey:@"ping"];
    [dict setObject:[Utils createMessageId] forKey:@"id"];
    [dict setObject:[[UserManager shareInstance] getUserId] forKey:@"userid"];
    [dict setObject:@"biran" forKey:@"terminalType"];
    NSString *jsonString = [[dict mj_JSONString] stringByAppendingString:@"\n"];
//    NSLog(@"send heart beat string = %@",jsonString);
    [self.socket writeData:[jsonString dataUsingEncoding:NSUTF8StringEncoding] withTimeout:-1 tag:-1];
}

#pragma mark- 代理处理

- (void)addDelegate:(id<SocketManagerDelegate>)delegate {
    [self.delegateSet addObject:delegate];
}

- (void)removeDelegate:(id<SocketManagerDelegate>)delegate {
    [self.delegateSet removeObject:delegate];
}

#pragma mark - GCDAsyncSocketDelegate
- (void)socket:(GCDAsyncSocket *)sock didConnectToHost:(NSString *)host port:(uint16_t)port {
    BRLog(@"连接成功 %@ %d",host,port);
    
    self.status = 0;
    
    BOOL isReconnection = (self.reconnectionTime > 0 ? YES : NO);
    //重新自动登录
    for (id <SocketManagerDelegate> delegate in [self.delegateSet copy]) {
        if ([delegate respondsToSelector:@selector(socketManagerDidConnectSuccess:)]) {
            [delegate socketManagerDidConnectSuccess:isReconnection];
        }
    }
    
    if (self.timer == nil) {
        self.timer = [NSTimer scheduledTimerWithTimeInterval:kPingTime target:self selector:@selector(sendHeartBeat) userInfo:nil repeats:YES];
    }
    
    self.reconnectionTime = 0;
    [sock readDataWithTimeout:-1 tag:-1];
}

- (void)socketDidDisconnect:(GCDAsyncSocket *)sock withError:(NSError *)err {
    NSLog(@"socket 断开连接 = %@",err.description);
    //连接失败 进行重连
    self.status = -1;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.reconnectionTime++;
        if (!sock.isConnected) {
            [self reConnection];
        }
    });
}

#pragma mark 写入数据成功 重新开启允许读取数据
- (void)socket:(GCDAsyncSocket *)sock didWriteDataWithTag:(long)tag {
    [self beginReadDataTimeOut:-1 tag:tag];
}

#pragma mark - 接收消息
- (void)socket:(GCDAsyncSocket *)sock didReadData:(NSData *)data withTag:(long)tag {
    NSString *getstring = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    BRSocketReceiveBaseModel *model = [BRSocketReceiveBaseModel mj_objectWithKeyValues:getstring];
    if ([model.code isEqualToString:BRApiPing]) {
//        NSLog(@"receive heart beat string = %@",getstring);
        //心跳处理
        id responseObject = [getstring jsonValueDecoded];
        //时间校验
        if ([responseObject objectForKey:@"SERVER_CREATED_TIME"]) {
            [Config storeAmendmentedTimeByServerTime:[responseObject objectForKey:@"SERVER_CREATED_TIME"]];
        }
        //token
        if ([responseObject objectForKey:@"sessionToken"]) {
            [[Config shareInstance] setSessionToken:[responseObject objectForKey:@"sessionToken"]];
        }
    }
    else {
        [self messageDispatch:getstring baseModel:model];
    }
}

- (void)beginReadDataTimeOut:(long)timeOut tag:(long)tag {
    [self.socket readDataToData:[GCDAsyncSocket LFData] withTimeout:timeOut maxLength:0 tag:tag];
}

#pragma mark- 消息分发
- (void)messageDispatch:(NSString *)message baseModel:(BRSocketReceiveBaseModel *)baseModel{
    NSLog(@"接收消息 message == %@",message);
    
    if ([baseModel.code isEqualToString:BRApiLogin]) {
        //登录
        if ([baseModel.resultCode isEqualToString:BRCodeSuccess]) {
            //连接成功
            BRLoginModel *model = [BRLoginModel mj_objectWithKeyValues:message];
            
            //获取token
            if (![model.sessionToken isEqualToString:@""]) {
                [[Config shareInstance] setSessionToken:model.sessionToken];
            }
            //获取问诊单地址
            if (![model.REQ_WX_H5 isEqualToString:@""]) {
                [Config storeWzdLinkUrl:model.REQ_WX_H5];
            }
            
            for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                if ([delegate respondsToSelector:@selector(socketManagerDidLoginSuccess:)]) {
                    [delegate socketManagerDidLoginSuccess:model];
                }
            }
        }
        //登录失败
        else {
            BRError *error = [BRError errorWithCode:[baseModel.resultCode integerValue] description:baseModel.resultDescription];
            
            if ([baseModel.resultDescription isEqualToString:@"登录失败,请重新登录!"]) {
                for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                    if ([delegate respondsToSelector:@selector(socketManagerDidSDKLoginConflict)]) {
                        [delegate socketManagerDidSDKLoginConflict];
                    }
                }
            }
            else {
                for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                    if ([delegate respondsToSelector:@selector(socketManagerDidLoginFailed:)]) {
                        [delegate socketManagerDidLoginFailed:error];
                    }
                }
            }
        }
    }
    else if ([baseModel.code isEqualToString:BRApiLogout]){
        
    }
    //请求开始接收消息
    else if ([baseModel.code isEqualToString:BRApiRequestReceiveMessage]){
        if ([baseModel.resultCode isEqualToString:BRCodeSuccess]) {
            NSLog(@"请求开始接收消息成功 0025 successful");
            //调用一次ping 用于更新时间和sessionToken
            [self sendHeartBeat];
        }
        else {
            for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                if ([delegate respondsToSelector:@selector(socketManagerDidRequestReceiveMessageFailed)]) {
                    [delegate socketManagerDidRequestReceiveMessageFailed];
                }
            }
        }
        
    }
    //发送消息后返回的
    else if (baseModel.resultCode && [baseModel.code isEqualToString:BRApiMessage]){
        BRMessagesModel *model = [BRMessagesModel mj_objectWithKeyValues:message];
        
        [self messageSendSuccessful:model];
        
        if ([baseModel.resultCode isEqualToString:BRCodeSuccess]) {
            
            for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                if ([delegate respondsToSelector:@selector(socketManagerDidSDKReceiveSendedMessages:)]) {
                    [delegate socketManagerDidSDKReceiveSendedMessages:model];
                }
            }
            
        }else{
            
        }
    }
    //接收到他人发送的消息
    else if (!baseModel.resultCode && [baseModel.code isEqualToString:BRApiMessage]) {
        
        BRMessagesModel *models = [BRMessagesModel mj_objectWithKeyValues:message];
        if ([models.messages count] > 0) {
            for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                if ([delegate respondsToSelector:@selector(socketManagerDidSDKReceiveMessages:)]) {
                    [delegate socketManagerDidSDKReceiveMessages:models];
                }
            }
        }
    }
    //接收历史消息
    else if (baseModel.resultCode && [baseModel.code isEqualToString:BRApiGetMessageHistoryList]){
        BRMessagesModel *models = [BRMessagesModel mj_objectWithKeyValues:message];
//        NSLog(@"历史记录 = %@",message);
        //models.messages 即使为空也需要发送  显示界面需要知道拉取已经完成
        for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(socketManagerDidSDKReceiveHistoryMessages:)]) {
                [delegate socketManagerDidSDKReceiveHistoryMessages:models];
            }
        }
    }
    //消息撤回
    else if ([baseModel.code isEqualToString:BRApiWithdrawMessage]) {
        if ([baseModel.resultCode isEqualToString:@"0000"]) {
            for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                if ([delegate respondsToSelector:@selector(socketManagerDidSDKReceiveWithdrawedFromId:)]) {
                    [delegate socketManagerDidSDKReceiveWithdrawedFromId:baseModel.fromId];
                }
            }
        }
    }
    //最近会话列表
    else if ([baseModel.code isEqualToString:BRApiGetLatestSessionsList]) {
        BRSessionListModel *sessionListModel = [BRSessionListModel mj_objectWithKeyValues:message];
        
        if ([sessionListModel.list count] > 0) {
            if ([baseModel.resultCode isEqualToString:@"0000"]) {
                for (id <SocketManagerDelegate> delegate in [self.delegateSet copy]) {
                    if ([delegate respondsToSelector:@selector(socketManagerDidSDKReceiveSessionLists:)]) {
                        [delegate socketManagerDidSDKReceiveSessionLists:sessionListModel];
                    }
                }
            }
        }
    }
    //登录冲突
    else if ([baseModel.code isEqualToString:BRApiLoginConflict]) {
        for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(socketManagerDidSDKLoginConflict)]) {
                [delegate socketManagerDidSDKLoginConflict];
            }
        }
    }
    else if ([baseModel.code isEqualToString:@"0031"] || [baseModel.code isEqualToString:@"0032"] || [baseModel.code isEqualToString:@"0033"] || [baseModel.code isEqualToString:@"0034"] || [baseModel.code isEqualToString:@"0036"] || [baseModel.code isEqualToString:@"0037"]){
        for (id<SocketManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(socketManagerDidBusinessInfo:)]) {
                [delegate socketManagerDidBusinessInfo:message];
            }
        }
    }
}

- (void)socketDidSecure:(GCDAsyncSocket *)sock {
    NSLog(@"socket secure success");
}

- (void)disConnect {
    [self.socket disconnect];
}

@end
