//
//  UserManager.m
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "UserManager.h"
#import "SocketManager.h"
#import "UIDevice+YYAdd.h"

#import "BRLoginModel.h"
#import "BRTablePharmacopeia.h"
#import "IMDataBaseManager.h"
#import "IMClient.h"
#import "IMSDKHelper.h"

#import <ReactiveObjC/ReactiveObjC.h>

static UserManager *userManager = nil;

@interface UserManager ()<SocketManagerDelegate>
@property (copy, nonatomic) void (^updateInfo)(NSString *info);
@property (copy, nonatomic) void (^successHandle)();
@property (copy, nonatomic) void(^successfulHandle)(ConfigInfo *info);
@property (copy, nonatomic) void (^failedHandle)(BRError *error);

@end

@implementation UserManager

+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        userManager = [[UserManager alloc] init];
        
        [[SocketManager shareInstance] addDelegate:userManager];
    });
    return userManager;
}

- (BOOL)isLogin {
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    BRUserIsLogin isLogin = [[userDefault objectForKey:kUserDefaultIsLogin] integerValue];
    if (isLogin == BRUserIsLoginYes) {
        return YES;
    }else{
        return NO;
    }
}

- (void)loginUsername:(NSString *)username password:(NSString *)password sms:(NSString *)sms success:(void (^)(ConfigInfo *))success failed:(void (^)(BRError *))failed {
    
    _successfulHandle = success;
    _failedHandle = failed;
    
    NSDictionary *dictionary = [self getLoginSendDictWithUsername:username password:password sms:sms];
    
    [[SocketManager shareInstance] sendDataDict:dictionary];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(15 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        BRError *error = [BRError errorWithCode:BRErrorCodeTimeOut description:@"请求超时"];
        if (_failedHandle) {
            _failedHandle(error);
            _failedHandle = nil;
        }
    });
    
}



- (void)logout {
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    [dictionary setObject:BRApiLogout forKey:@"code"];
    [[SocketManager shareInstance] sendDataDict:dictionary];
    
    NSUserDefaults *userdefaults = [NSUserDefaults standardUserDefaults];
    [userdefaults setObject:@"" forKey:kUserDefaultUserId];
    [userdefaults setObject:@"" forKey:kUserDefaultName];
    [userdefaults setObject:@"" forKey:kUserDefaultUserName];
    [userdefaults setObject:@"" forKey:kUserDefaultUserType];
    [userdefaults setObject:@"" forKey:kUserDefaultPlainPassword];
    [userdefaults setObject:@"" forKey:kUserDefaultTelephone];
    [userdefaults setObject:@"" forKey:kUserDefaultStatus];
    [userdefaults setObject:@"" forKey:kUserDefaultThirdType];
    [userdefaults setObject:@"" forKey:kUserDefaultWXOpenid];
    [userdefaults setObject:@(BRUserIsLoginNo) forKey:kUserDefaultIsLogin];
    [userdefaults setObject:@"" forKey:kUserDefaultApp_ShowMedicalServiceFeeRule];
    [userdefaults synchronize];  
}

#pragma mark - SocketManagerDelegate
- (void)socketManagerDidLoginSuccess:(BRLoginModel *)model {
    
    //保存是否显示医技服务费
    [self storeApp_ShowMedicalServiceFeeRule:model.app_ShowMedicalServiceFeeRule];
    
    UserInfo *userInfo = model.user;
    userInfo.app_isHaveTakePictureAuth = model.app_isHaveTakePictureAuth;
    [self storeUserInfo:userInfo];
    
    
    [Config shareInstance].app_colseWelcome = model.app_colseWelcome;
    [Config shareInstance].app_isYspAuth = model.app_isYspAuth;

    //保存用户是否已完善资料
    if (model.is_perfected) {
        [Config storeisNeedCompleteUserData:NO];
    }
    else{
        [Config storeisNeedCompleteUserData:YES];
    }
    
    ConfigInfo *configInfo = model.config;
    
    _failedHandle = nil;
    if (_successfulHandle) {
        _successfulHandle(configInfo);
        _successHandle = nil;
    }
    
    //如果为自动登录
    if ([Config getisAutoLogin]) {
        [[IMClient shareInstance] startRequestReceiveMessage];
        
        //则开始下载数据  更新数据
        [[Config shareInstance] updateDrugProductInfo:configInfo];
        [[Config shareInstance] updateAreaInfo:configInfo];
        //更新最近的会话列表
//        [[IMClient shareInstance].sessionManager updateSessionListFromServer];
        [[IMClient shareInstance].contactManager updateContactListFromServer];
        [[IMClient shareInstance].sessionManager updateUnReadMessagesFromServer];
    }
    //非自动登录
    else {
        //更新会话列表
        [[IMClient shareInstance].sessionManager updateSessionListFromServer];
    }
}

- (void)loginAutomatic {
    //发送登录请求
    NSDictionary *dictionary = [self getLoginSendDictWithUsername:self.getUserName password:@"" sms:nil];
    [[SocketManager shareInstance] sendDataDict:dictionary];
}

- (void)socketManagerDidLoginFailed:(BRError *)error {
    //替换显示文字
    if ([error.errorDescription hasPrefix:@"user not exists"]) {
        error.errorDescription = @"该手机号尚未注册";
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        _successHandle = nil;
        if (_failedHandle) {
            _failedHandle(error);
        }
    });
    
    //自动登录时登录失败
    if ([Config getisAutoLogin]) {
        [self logout];
        [Utils startLogin];
        NSLog(@"auto login login failed");
    }
}

- (void)socketManagerDidRequestReceiveMessageFailed {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if ([self isLogin]) {
            [[IMClient shareInstance] startRequestReceiveMessage];
        }
    });
}
#pragma mark - socket 连接成功 或者断开后重新连接成功 自动登录
- (void)socketManagerDidConnectSuccess:(BOOL)isReConnection {
    //断开后重新连接
    if (isReConnection) {
        //请求开始接收数据
        if ([self isLogin]) {
            //开始登录
            NSString *username = [self getUserName];
//            NSString *password = [self getPlainPassword];
            NSDictionary *params = [self getLoginSendDictWithUsername:username password:@"" sms:nil];
            [[SocketManager shareInstance] sendDataDict:params];
            
            if ([Config getisAutoLogin]) {
                //开始接收消息
                [[IMClient shareInstance] startRequestReceiveMessage];
            }
        }
    }
    //首次连接成功
    else {
        //如果为自动登录
        if ([Config getisAutoLogin]) {
            //开始登录
            if ([self isLogin]) {
                NSString *username = [self getUserName];
//                NSString *password = [self getPlainPassword];
                NSDictionary *dictionary = [self getLoginSendDictWithUsername:username password:@"" sms:nil];
                [[SocketManager shareInstance] sendDataDict:dictionary];
            }
        }
        //非自动登录  暂不用执行操作
        else {
            
        }
    }
}

#pragma mark - Get

- (NSString *)getUserId {
    if ([self isLogin] && [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultUserId]) {
        return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultUserId];
    }else{
        return @"anonymous";
    }
}

- (NSString *)getUserName {
    return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultUserName];
}

- (NSString *)getName {
    return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultName];
}

- (NSString *)getPlainPassword {
    return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultPlainPassword];
}

- (NSString *)getTelephone {
    if ([[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultTelephone]) {
        return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultTelephone];
    }
    else {
        return @"";
    }
}

- (NSString *)getUserInfoByKey:(NSString *)key {
    return [[NSUserDefaults standardUserDefaults] objectForKey:key];
}

- (NSString *)getApp_ShowMedicalServiceFeeRule {
    return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultApp_ShowMedicalServiceFeeRule];
}

- (NSString *)getWXOpenId {
    if ([[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultWXOpenid]) {
        return [[NSUserDefaults standardUserDefaults] objectForKey:kUserDefaultWXOpenid];
    }
    else {
        return @"";
    }
}

#pragma mark - Store

- (void)storeUserInfo:(UserInfo *)userInfo {
    
    NSUserDefaults *userdefaults = [NSUserDefaults standardUserDefaults];
    
    [userdefaults setObject:userInfo.userId forKey:kUserDefaultUserId];
    [userdefaults setObject:userInfo.name forKey:kUserDefaultName];
    [userdefaults setObject:userInfo.username forKey:kUserDefaultUserName];
    [userdefaults setObject:@(userInfo.userType) forKey:kUserDefaultUserType];
    [userdefaults setObject:userInfo.plainPasssword forKey:kUserDefaultPlainPassword];
    [userdefaults setObject:userInfo.telephone forKey:kUserDefaultTelephone];
    [userdefaults setObject:userInfo.status forKey:kUserDefaultStatus];
    [userdefaults setObject:@(userInfo.thirdType) forKey:kUserDefaultThirdType];
    [userdefaults setObject:@(BRUserIsLoginYes) forKey:kUserDefaultIsLogin];
    //微信openId
    [userdefaults setObject:userInfo.wxOpenId forKey:kUserDefaultWXOpenid];
    
    [userdefaults setObject:userInfo.app_isHaveTakePictureAuth forKey:kUserDefaultApp_isHaveTakePictureAuth];
//    [userdefaults setObject:userInfo.currentAuthenticationState forKey:kUserDefaultcurrentAuthenticationState];    
    [[NSNotificationCenter defaultCenter] postNotificationName:kUserDefaultApp_isHaveTakePictureAuth object:userInfo.app_isHaveTakePictureAuth];
    
    [userdefaults synchronize];
}

- (void)setUserInfoWithValue:(id)value key:(NSString *)key {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:value forKey:key];
    [userDefaults synchronize];
}

- (void)updateUserInfo:(UserInfo *)userInfo {
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    if (userInfo.name) {
        [userDefaults setObject:userInfo.name forKey:kUserDefaultName];
    }
    if (userInfo.address) {
        [userDefaults setObject:userInfo.address forKey:kUserDefaultAddress];
    }
    if (userInfo.areaCode) {
        [userDefaults setObject:userInfo.areaCode forKey:kUserDefaultAreacode];
    }
    if (userInfo.description) {
        [userDefaults setObject:userInfo.desc forKey:kUserDefaultDescription];
    }
    if (userInfo.goodAt) {
        [userDefaults setObject:userInfo.goodAt forKey:kUserDefaultGoodAt];
    }
    if (userInfo.handleUrl) {
        [userDefaults setObject:userInfo.handleUrl forKey:kUserDefaultHandleUrl];
    }
    if (userInfo.hospitalName) {
        [userDefaults setObject:userInfo.hospitalName forKey:kUserDefaultHospitalName];
    }
    if (userInfo.isAuthentication) {
        [userDefaults setObject:userInfo.isAuthentication forKey:kUserDefaultIsAuthentication];
    }
    if (userInfo.isDiTui) {
        [userDefaults setObject:userInfo.isDiTui forKey:kUserDefaultIsDiTui];
    }
    if (userInfo.isPerfected) {
        [userDefaults setObject:userInfo.isPerfected forKey:kUserDefaultIsPrefected];
    }
    if (userInfo.lvlName) {
        [userDefaults setObject:userInfo.lvlName forKey:kUserDefaultLvlName];
    }
    if (userInfo.notPayOrderNum) {
        [userDefaults setObject:userInfo.notPayOrderNum forKey:kUserDefaultNotPayOrderNum];
    }
    if (userInfo.paperNo) {
        [userDefaults setObject:userInfo.paperNo forKey:kUserDefaultPaperNo];
    }
    if (userInfo.privatePassword) {
        [userDefaults setObject:userInfo.privatePassword forKey:kUserDefaultPrivatePassword];
    }
    if (userInfo.sex) {
        [userDefaults setObject:userInfo.sex forKey:kUserDefaultSex];
    }
    if (userInfo.stockDay) {
        [userDefaults setObject:userInfo.stockDay forKey:kUserDefaultStockDay];
    }
    if (userInfo.title) {
        [userDefaults setObject:userInfo.title forKey:kUserDefaultTitle];
    }
    if (userInfo.type) {
        [userDefaults setObject:userInfo.type forKey:kUserDefaultType];
    }
    if (userInfo.userState) {
        [userDefaults setObject:userInfo.userState forKey:kUserDefaultUserState];
    }
    if (userInfo.currentAuthenticationState) {
        [userDefaults setObject:userInfo.currentAuthenticationState forKey:kUserDefaultcurrentAuthenticationState];
    }
    if (userInfo.bagde) {
        [userDefaults setObject:userInfo.bagde forKey:kUserDefaultbagde];
    }
    if (userInfo.paperNoNew) {
        [userDefaults setObject:userInfo.paperNoNew forKey:kUserDefaultpaperNoNew];
    }
    if (userInfo.companyVideoUrl) {
        [userDefaults setObject:userInfo.companyVideoUrl forKey:kUserDefaultcompanyVideoUrl];
    }

    if (userInfo.keyBoardNumber) {
        [userDefaults setObject:userInfo.keyBoardNumber forKey:kUserDefaultkeyBoardNumber];
    }
    
    if (userInfo.wxOpenId) {
        [userDefaults setObject:userInfo.wxOpenId forKey:kUserDefaultWXOpenid];
    }
    
    [userDefaults synchronize];
}

- (void)storeApp_ShowMedicalServiceFeeRule:(NSString *)serviceFeeRule {
    
    NSLog(@"serviceFeeRule is %@",serviceFeeRule);
    
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setObject:serviceFeeRule forKey:kUserDefaultApp_ShowMedicalServiceFeeRule];
    [userDefaults synchronize];
    
}

- (void)updateApp_ShowMedicalServiceFeeRule:(NSString *)serviceFeeRule {
    [self storeApp_ShowMedicalServiceFeeRule:serviceFeeRule];
}

#pragma mark - 获取登录提交参数
- (NSDictionary *)getLoginSendDictWithUsername:(NSString *)username password:(NSString *)password sms:(NSString *)sms {
    NSString *device = [[[UIDevice currentDevice] machineModelName] stringByReplacingOccurrencesOfString:@"," withString:@"_"];

    if (!device || [device isEqualToString:@""]) {
        device = @"iPhone";
    }

    NSString *osName = @"IOS";

    //password 不等于 @"" 时  加密
    if (![password isEqualToString:@""]) {
        password = [[password md5String] uppercaseString];
    }

    NSString *registrationId = [Config getregistrationId];

    NSString *deviceTokenString = @"";

    NSString *pwdInfoString = @"";

    if (![registrationId isEqualToString:@""]) {
        deviceTokenString = [NSString stringWithFormat:@",deviceToken:%@",registrationId];
    }

    if (![password isEqualToString:@""]) {
        pwdInfoString = [NSString stringWithFormat:@"pwd:%@,",password];
    }
    else {
        pwdInfoString = @"";
    }

    NSString *passwordString = @"";

    // 检查是否为号码认证登录（密码和验证码都为空）
    if ([password isEqualToString:@""] && [sms isEqualToString:@""]) {
        // 号码认证登录：统一使用设备唯一标识，保持首次和再次登录格式一致
        NSString *deviceUniqueId = [Config uuid];
        passwordString = [NSString stringWithFormat:@"device:%@,deviceToken:%@",deviceUniqueId,deviceUniqueId];
    }
    else if (password) {
        // 密码登录：恢复原有逻辑
        passwordString = [NSString stringWithFormat:@"%@device:%@%@",pwdInfoString,device,deviceTokenString];
    }
    else if (sms){
        // 验证码登录：恢复原有逻辑
        passwordString = [NSString stringWithFormat:@"sms:%@,device:%@%@",sms,device,deviceTokenString];
    }

    NSString *uuid = [Config uuid];

    NSLog(@"uuid = %@",uuid);

    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    [dictionary setObject:BRApiLogin forKey:@"code"];
    [dictionary setObject:username forKey:@"username"];
    [dictionary setObject:passwordString forKey:@"password"];
    [dictionary setObject:osName forKey:@"os"];
    [dictionary setObject:@"1" forKey:@"userType"];
    [dictionary setObject:@"Smack" forKey:@"device"];
    [dictionary setObject:@"BUSY" forKey:@"status"];

    [dictionary setObject:uuid forKey:@"uuid"];

    NSLog(@"login params = %@",dictionary);

    return dictionary;
}



- (void)writeQRCodeImageToFile:(UIImage *)image
{
    NSArray *dirArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *path = [dirArray firstObject];
    path = [path stringByAppendingPathComponent:[NSString stringWithFormat:@"%@%@",[self getUserId],kUserQRCodePath]];
    NSData *imageData = UIImageJPEGRepresentation(image, 1.0);
    [imageData writeToFile:path atomically:YES];
}

- (UIImage *)getQRCodeImageWithFile
{
    NSArray *dirArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *path = [dirArray firstObject];
    path = [path stringByAppendingPathComponent:[NSString stringWithFormat:@"%@%@",[self getUserId],kUserQRCodePath]];
    if([[NSFileManager defaultManager] fileExistsAtPath:path]){
        NSData *picData = [NSData dataWithContentsOfFile:path];
        UIImage *image = [UIImage imageWithData:picData];
        return image;
    }
    
    return nil;
}

- (void)removeQRCodeImage
{
    NSArray *dirArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *path = [dirArray firstObject];
    path = [path stringByAppendingPathComponent:[NSString stringWithFormat:@"%@%@",[self getUserId],kUserQRCodePath]];
    if([[NSFileManager defaultManager] fileExistsAtPath:path])
    {
        [[NSFileManager defaultManager]  removeItemAtPath:path error:nil];
    }
}

- (BOOL)isHaveQRCodeImage {
    NSArray *dirArray = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *path = [dirArray firstObject];
    path = [path stringByAppendingPathComponent:[NSString stringWithFormat:@"%@%@",[self getUserId],kUserQRCodePath]];
    if([[NSFileManager defaultManager] fileExistsAtPath:path])
    {
        return YES;
    }
    return NO;
}

@end
