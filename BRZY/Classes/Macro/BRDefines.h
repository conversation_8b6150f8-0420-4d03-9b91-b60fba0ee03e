//
//  BRDefines.h
//  BRZY
//
//  Created by  xujiangtao on 2017/8/29.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#ifndef BRDefines_h
#define BRDefines_h

/******************* 服务器信息 *****************/

//正式环境
#define kSocketAddress              @"im.haoniuzhongyi.top"
#define kServerDomain               @"https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController"
#define kDrugStoreAgreeDomain       @"https://www.haoniuzhongyi.top/easydoctorv2-ws/wxHealthCareShop/queryUserNameById?userid="
#define kSocketSSLPort    5333


#define kDomain                   [Config getWzdLinkUrl]

//===============用户协议  隐私政策 =======
#define kPrivacyUrl     @"http://www.brzhongyi.com/privacy-policy.html"
#define kUserAgreementUrl   @"http://www.brzhongyi.com/doctoragreement.html"

#define kUniversialLink @"https://brzy-dzsj.oss-cn-beijing.aliyuncs.com/brzy/"

//=====================================================

//消息列表显示多少天内的
#define kMessageListShowDateDays            360
//联系人列表显示多少天内的
#define kContactListShowDateDays            360


/******************* 小然 *********************/
//小然客服
#define kXiaoRanId          @"1000"
#define kXiaoRanHeadImg     @"xiaoran"
/******************* 系统消息 *********************/

#define kSystemId           @"0"

#define kBadgeMessageMax    99

/******************* 弹框 *********************/
#define kDialogAutoUpdate       @"kDialogAutoUpdate"
#define kDialogFuncIntroduce    @"kDialogFuncIntroduce"
#define kDialogNotice           @"kDialogNotice"
#define kDialogActivity         @"kDialogActivity"


/******************* App 通知 *********************/

//从添加或修改常用问题返回交流界面弹出常用问题弹出框
#define kNotificationShowFrequentShowView                       @"kNotificationShowFrequentShowView"
//从问诊单h5页面返回后跳转到去用药界面
#define kNotificationGoToUseDrugPage                            @"kNotificationGoToUseDrugPage"
//显示活动页详情
#define kNotificationShowActivityPage                           @"kNotificationShowActivityPage"
//重新加载患者信息
#define kNotificationReloadDocumentList                         @"kNotificationReloadDocumentList"
//搜索页面点击经典方处理
#define kNotificationDealSearchClassicPrescription              @"kNotificationDealSearchClassicPrescription"
//
#define kNotificationSearchViewPop                              @"kNotificationSearchViewPop"
//更新患者列表显示的患者数量
#define kNotificationPatientCountChange                         @"kNotificationPatientCountChange"

//获取微信code通知
#define kNotificationWechatAuth                                 @"kNotificationWechatAuth"

//医案界面患者切换通知
#define kNotificationPatientDocumentChangePatient               @"kNotificationPatientDocumentChangePatient"

//用药界面患者切换通知
#define kNotificationPrescriptionChangePatient                  @"kNotificationPrescriptionChangePatient"

/******************* IM 通知 *********************/

//重新从本地数据库中获取会话列表并更新UI界面
#define kIMNotificationUpdateSessionListFromDataBase            @"kIMNotificationUpdateSessionListFromDataBase"
//重新从服务器上获取最新的会话列表  根据时间获取新增的会话
#define kIMNotificationUpdateSessionListFromRemoteServer        @"kIMNotificationUpdateSessionListFromRemoteServer"
//重新从服务器上获取最新的联系人列表  根据时间获取新增的联系人
#define kIMNotificationUpdateContactListFromRemoteServer        @"kIMNotificationUpdateContactListFromRemoteServer"
//重新从本地数据库中获取联系人列表并更新UI界面
#define kIMNotificationUpdateContactListFromDataBase            @"kIMNotificationUpdateContactListFromDataBase"
//更新总的未读消息条数
#define kIMNotificationUpdateSumUnReadCount                     @"kIMNotificationUpdateSumUnReadCount"
//更新患者信息通知
#define kIMNotificationUPdatePatientInfo                        @"kIMNotificationUPdatePatientInfo"

/******************* IM 数据库相关定义 ************/
#define IMDocumentPath    [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0]
#define IMDataBasePath    [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"im_database_file"]
#define IMVoiceFolder     @"im_voice_folder"
#define IMImageFolder     @"im_image_folder"

#define IMDataBaseName  @"brzy.sqlite"

#define IMContactTable  @"contact_table"
#define IMSessionTable  @"session_table"
#define IMMessageTable  @"message_table"
/****************** IM Cell UI类型定义 *********************/
#define IMMessageCellTypeDate               @"Date"
#define IMMessageCellTypeSystem             @"System"
#define IMMessageCellTypeText               @"Text"
#define IMMessageCellTypeImage              @"Image"
#define IMMessageCellTypeAudio              @"Audio"
#define IMMessageCellTypeVideo              @"Video"
#define IMMessageCellTypeFile               @"File"
#define IMMessageCellTypeWzdQuestion        @"WZD_QUESTION" //问诊单（首诊）问题
#define IMMessageCellTypeWzdAnswer          @"WZD_ANSWER"   //问诊单（首诊）答案
#define IMMessageCellTypeWzdFzQuestion      @"ZHSF_QUESTION"//问诊单（复诊）问题
#define IMMessageCellTypeWzdFzAnswer        @"ZHSF_ANSWER"  //问诊单（复诊）答案
#define IMMessageCellTypeSupplementQuestion @"SUPPLEMENT_QUESTION"//补充问题 问题
#define IMMessageCellTypeSupplementAnswer   @"SUPPLEMENT_ANSWER"  //补充问题 答案
#define IMMessageCellTypeStartChatDoctor    @"START_CHAT_DOCTOR" //医生开启会话
#define IMMessageCellTypeStartChatPatient   @"START_CHAT_PATIENT" //患者付费开启会话
#define IMMessageCellTypeFinishChatDoctor   @"FINISH_CHAT_DOCTOR" //医生结束会话
#define IMMessageCellTypeLogistics          @"LOGISTICS"    //物流信息
#define IMMessageCellTypeMsgRevoke          @"MSG_REVOKE"   //消息撤回

#define IMMessageCellTypeSessionStart       @"SESSION_START" //会话开始
#define IMMessageCellTypeSessionEnd         @"SESSION_END"   //会话结束
#define IMMessageCellTypeCustomSystem       @"CUSTOM_SYSTEM" //定制系统消息
/****************** IM 数据表字段 *********************/


#define IM_Table_Message            @"im_t_message"
#define IM_Table_Session            @"im_t_session"
#define IM_Table_Contact            @"im_t_contact"

#define BR_Table_Pharmacopie        @"I_Table_Pharmacopie"
#define BR_Table_Prescription       @"br_table_Prescription"

//session 表
#define S_F_Id                      @"f_id"
#define S_F_SessionId               @"f_sessionId"
#define S_F_SessionType             @"f_sessionType"
#define S_F_CreateTime              @"f_createTime"
#define S_F_IsRemingding            @"f_isRemingding"
#define S_F_LastMessageId           @"f_lastMessageId"
#define S_F_LastMessageMimeType     @"f_lastMessageMimeType"
#define S_F_LastMessageCreateTime   @"f_lastMessageCreateTime"
#define S_F_LastMessageText         @"f_lastMessageText"

//message 表
#define M_F_Id                      @"f_id"
#define M_F_MessageId               @"f_messageId"
#define M_F_SessionId               @"f_sessionId"
#define M_F_MimeType                @"f_mimeType"
#define M_F_SessionType             @"f_sesionType"
#define M_F_CreateTime              @"f_createTime"
#define M_F_SrcAccount              @"f_srcAccount"
#define M_F_DstAccount              @"f_dstAccount"
#define M_F_Content                 @"f_content"
#define M_F_FilePath                @"f_filePath"
#define M_F_Status                  @"f_status"

//contact 表
#define C_F_Id                      @"f_id"
#define C_F_UserName                @"f_username"
#define C_F_Age                     @"f_age"
#define C_F_HeadImageUrl            @"f_headImageUrl"
#define C_F_Nation                  @"f_nation"
#define C_F_Gender                  @"f_gender"
#define C_F_LastUpdateTime          @"f_lastUpdateTime"
#define C_F_GroupName               @"f_groupName"

/****************** 消息 Api 以及 状态 *********************/

#define BRCodeSuccess               @"0000"
#define BRApiLogin                  @"0002"
#define BRApiLogout                 @"0006"
#define BRApiPing                   @"0007"
#define BRApiMessage                @"0008"
#define BRApiCreateGrouping         @"0009"
#define BRApiModifyGrouping         @"0010"
#define BRApiDeleteGrouping         @"0011"
#define BRApiModifyFriend           @"0012A"
#define BRApiGetGroupingList        @"0017"
#define BRApiGetMessageHistoryList  @"0019"
#define BRApiWithdrawMessage        @"0023"
#define BRApiLoginConflict          @"0024"
#define BRApiRequestReceiveMessage @"0025"
#define BRApiMessageReceiveSuccess  @"0026"
#define BRApiStartOrEndSession      @"0027"
#define BRApiGetLatestSessionsList  @"0029"

/********************* 用户信息保存 ************************/

#define kUserDefaultUserId          @"kUserDefaultUserId"
#define kUserDefaultName            @"kUserDefaultName"
#define kUserDefaultUserName        @"kUserDefaultUserName"
#define kUserDefaultUserType        @"kUserDefaultUserType"
#define kUserDefaultPlainPassword   @"kUserDefaultPlainPassword"
#define kUserDefaultTelephone       @"kUserDefaultTelephone"
#define kUserDefaultStatus          @"kUserDefaultStatus"
#define kUserDefaultThirdType       @"kUserDefaultThirdType"
#define kUserDefaultUniqueValues    @"kUserDefaultUniqueValues"
#define kUserDefaultApp_isHaveTakePictureAuth @"kUserDefaultApp_isHaveTakePictureAuth"

#define kUserDefaultIsLogin         @"kUserDefaultIsLogin"

//微信openId
#define kUserDefaultWXOpenid        @"kUserDefaultWXOpenId"

//用户详细信息
#define kUserDefaultAddress         @"kUserDefaultAddress"
#define kUserDefaultAreacode        @"kUserDefaultAreacode"
#define kUserDefaultDescription     @"kUserDefaultDescription"
#define kUserDefaultGoodAt          @"kUserDefaultGoodAt"
#define kUserDefaultHandleUrl       @"kUserDefaultHandleUrl"
#define kUserDefaultHospitalName    @"kUserDefaultHospitalName"
#define kUserDefaultIsAuthentication @"kUserDefaultIsAuthentication"
#define kUserDefaultIsDiTui         @"kUserDefaultIsDiTui"
#define kUserDefaultIsPrefected     @"kUserDefaultIsPrefected"
#define kUserDefaultLvlName         @"kUserDefaultLvlName"
#define kUserDefaultNotPayOrderNum  @"kUserDefaultNotPayOrderNum"
#define kUserDefaultPaperNo         @"kUserDefaultPaperNo"
#define kUserDefaultPrivatePassword @"kUserDefaultPrivatePassword"
#define kUserDefaultSex             @"kUserDefaultSex"
#define kUserDefaultStockDay        @"kUserDefaultStockDay"
#define kUserDefaultTitle           @"kUserDefaultTitle"
#define kUserDefaultType            @"kUserDefaultType"
#define kUserDefaultUserState       @"kUserDefaultUserState"
#define kUserDefaultcurrentAuthenticationState  @"kUserDefaultcurrentAuthenticationState"
#define kUserDefaultbagde           @"kUserDefaultbagde"
#define kUserQRCodePath             @"kUserQRCodePath"
#define kUserDefaultpaperNoNew      @"kUserDefaultpaperNoNew"
#define kUserDefaultIfSendFZD       @"kUserDefaultIfSendFZD"
#define kUserDefaultcompanyVideoUrl @"kUserDefaultcompanyVideoUrl"
#define kUserDefaultfirstVideoUrl   @"kUserDefaultfirstVideoUrl"
#define kUserDefaultisChange        @"kUserDefaultisChange"
#define kUserDefaultisOrder         @"kUserDefaultisOrder"
#define kUserDefaultkeyBoardNumber  @"kUserDefaultkeyBoardNumber"
#define kUserDefaultApp_ShowMedicalServiceFeeRule @"kUserDefaultApp_ShowMedicalServiceFeeRule"

// 药方模板页面提示语显示状态
#define kUserDefaultClassicPrescriptionNoticeHidden @"kUserDefaultClassicPrescriptionNoticeHidden"
#define kUserDefaultCommonPrescriptionNoticeHidden @"kUserDefaultCommonPrescriptionNoticeHidden"

//*****************   app下载地址   ******************
#define kDownloadAPPUrl @"www.brzhongyi.com"

//SDK helper
#define kIMSDKHelperCacheName       @"kIMSDKHelperCacheName"
#define kIMSDKLastContactListUpdateTime         @"kIMSDKLastContactListUpdateTime"
#define kIMSDKLastSessionListUpdateTime         @"kIMSDKLastSessionListUpdateTime"


//**************** 保存用法用量 *********************

//======饮片等
//共多少剂
#define kUsageKandYType_All             @"kUsageKandYType_All"
//每日多少剂
#define kUsageKandYType_EveryDayAmount  @"kUsageKandYType_EveryDayAmount"
//分多少次服用
#define kUsageKandYType_times           @"kUsageKandYType_times"


//======膏方等
//每日多少次
#define kUsageOtherType_EveryDayTimes       @"kUsageOtherType_EveryDayTimes"
//每次多少克
#define kUsageOtherType_EveryTimesAmount    @"kUsageOtherType_EveryTimesAmount"
//预计多少天
#define kUsageOtherType_EstimateDays        @"kUsageOtherType_EstimateDays"


#define kUsageMethodKey  @"kUsageMethodKey"

#endif /* BRDefines_h */
