//
//  BRActionSheetView.m
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/11/21.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRActionSheetView.h"
#import "BRCashTypeInfoModel.h"

#define kContentDefaultHeight   100.0f
#define kBottonsMaxHeightCount  5

@interface BRActionSheetView ()

@property (nonatomic, strong) UIView *coverView;
@property (nonatomic, strong) UIButton *bottomButton;
@property (nonatomic, strong) UILabel *titleLabel;

@property (strong, nonatomic) UILabel *topButtonLabel;
@property (strong, nonatomic) UIImageView *topButtonImageView;

@property (nonatomic, assign) CGFloat viewHeight;  //self height
@property (nonatomic, assign) CGFloat bottomButtonHeight;

@property (nonatomic, strong) UIScrollView *buttonScrollView;
@property (nonatomic, strong) UIView *safeAreaBottomView; // 底部安全区域白色背景视图

@end

@implementation BRActionSheetView
@synthesize customView = _customView;

- (UIView *)titleView {
    if (!_titleView) {
        _titleView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kTitleViewDefaultHeight)];
        _titleView.backgroundColor = [UIColor br_backgroundColor];
        
        //头部添加线条
        UIView *lineView = [[UIView alloc] init];
        lineView.backgroundColor = [UIColor br_divisionLineColor];
        
        [_titleView addSubview:lineView];
        
        [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.and.right.equalTo(_titleView);
            make.bottom.mas_equalTo(@(0));
            make.height.mas_equalTo(@(1/kScreenScale));
        }];
        
        //配置titleView
        UIImage *closeBtnImage = [UIImage imageNamed:@"btn_action_close"];
        UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeButton setImage:closeBtnImage forState:UIControlStateNormal];
        
        [closeButton addTarget:self action:@selector(clickCloseButton:) forControlEvents:UIControlEventTouchUpInside];
        
        [_titleView addSubview:closeButton];
        
        //关闭按钮改为左侧
        [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(@(10));
            make.centerY.equalTo(_titleView);
            make.size.mas_equalTo(CGSizeMake(closeBtnImage.size.width, closeBtnImage.size.height));
        }];
        
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor br_textDarkBlueColor];
        _titleLabel.font = kFontRegular(17);
        _titleLabel.text = _title;
        [_titleView addSubview:_titleLabel];
        
        [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(_titleView);
        }];
        
        //操作按钮改为右侧
        [_titleView addSubview:self.topButtonImageView];
        [_titleView addSubview:self.topButtonLabel];
        
        [self.topButtonLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.mas_equalTo(@(-10));
            make.centerY.equalTo(_titleView);
        }];
        
        
        [self.topButtonImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.topButtonLabel.mas_left).with.offset(-3);
            make.centerY.equalTo(_titleView);
            make.size.mas_equalTo(CGSizeMake(20, 20));
        }];
        
        self.topButtonLabel.userInteractionEnabled = YES;
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            if (_clickRightCallBack) {
                _clickRightCallBack();
            }
            
            [self pressRightButton];
            
        }];
        [self.topButtonLabel addGestureRecognizer:tapGesture];
        
        // 添加右上角按钮
        if (self.rightButton) {
            [_titleView addSubview:self.rightButton];
            
            [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_equalTo(@(-10));
                make.centerY.equalTo(_titleView);
                make.size.mas_equalTo(self.rightButton.frame.size);
            }];
        }
    }
    return _titleView;
}

// 当设置 rightButton 时，重新加载 titleView
- (void)setRightButton:(UIButton *)rightButton {
    _rightButton = rightButton;
    
    // 如果 titleView 已经存在，需要移除旧的 rightButton 并添加新的
    if (_titleView) {
        // 将已有的 rightButton 移除（如果存在）
        UIButton *oldRightButton = [_titleView viewWithTag:1002]; // 使用唯一标记标识 rightButton
        if (oldRightButton) {
            [oldRightButton removeFromSuperview];
        }
        
        if (_rightButton) {
            _rightButton.tag = 1002; // 设置标记以便于识别
            [_titleView addSubview:_rightButton];
            
            [_rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.mas_equalTo(@(-10));
                make.centerY.equalTo(_titleView);
                make.size.mas_equalTo(_rightButton.frame.size);
            }];
        }
    }
}

- (UILabel *)topButtonLabel {
    if (!_topButtonLabel) {
        _topButtonLabel = [[UILabel alloc] init];
        [_topButtonLabel setFont:kFontLight(17)];
        _topButtonLabel.textColor = [UIColor br_buttonTextBlueColor];
    }
    return _topButtonLabel;
}

- (UIImageView *)topButtonImageView {
    if (!_topButtonImageView) {
        _topButtonImageView = [[UIImageView alloc] init];
        _topButtonImageView.contentMode = UIViewContentModeScaleAspectFit;
    }
    return _topButtonImageView;
}

- (UIButton *)bottomButton {
    if (!_bottomButton) {
        _bottomButton = [[UIButton alloc] init];
        [_bottomButton setBackgroundImage:[Utils createImageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
        [[_bottomButton titleLabel] setFont:kFontLight(18)];
        [_bottomButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_bottomButton addTarget:self action:@selector(clickBottonButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _bottomButton;
}

- (UIView *)customView {
    if (!_customView) {
        _customView = [[UIView alloc] initWithFrame:CGRectMake(0, kTitleViewDefaultHeight, SCREEN_WIDTH, kContentDefaultHeight)];
        _customView.backgroundColor = [UIColor whiteColor];
    }
    return _customView;
}
#pragma mark - show
- (void)show{
    
    _viewHeight = kTitleViewDefaultHeight + self.customView.bounds.size.height + _bottomButtonHeight;
    
    self.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, _viewHeight);
    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    if (!_coverView) {
        _coverView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        _coverView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        [app.window addSubview:_coverView];
        
        _coverView.userInteractionEnabled = YES;
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBgViewGesture:)];
        [_coverView addGestureRecognizer:tapGesture];
    }
    
    [self addSubview:self.titleView];
    [self addSubview:self.customView];
    
    //是否增加底部button
    if (self.bottomTitle || self.bottomIcon) {
        [self addSubview:self.bottomButton];
    }
    
    // 添加底部安全区域白色背景视图
    if (!_safeAreaBottomView) {
        _safeAreaBottomView = [[UIView alloc] init];
        _safeAreaBottomView.backgroundColor = [UIColor whiteColor];
    }
    [app.window addSubview:_safeAreaBottomView];
    [app.window addSubview:self];
    
    // 初始化底部安全区域视图的位置（在屏幕外）
    _safeAreaBottomView.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, kTabbarSafeBottomMargin);
    
    //显示
    [UIView animateWithDuration:.5 delay:0 usingSpringWithDamping:.8 initialSpringVelocity:0.5 options:(UIViewAnimationCurveEaseInOut|UIViewAnimationOptionBeginFromCurrentState) animations:^{
        
        self.frame = CGRectMake(0, kScreenHeight - _viewHeight - kTabbarSafeBottomMargin, SCREEN_WIDTH, _viewHeight);
        // 底部安全区域视图与弹窗底部对齐
        _safeAreaBottomView.frame = CGRectMake(0, kScreenHeight - kTabbarSafeBottomMargin, SCREEN_WIDTH, kTabbarSafeBottomMargin);
        _coverView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        
    } completion:^(BOOL finished) {
        
    }];
    
}

- (void)showInView:(UIView *)inView {
    _viewHeight = kTitleViewDefaultHeight + self.customView.bounds.size.height + _bottomButtonHeight;
    self.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, _viewHeight);
    
    if (!_coverView) {
        CGRect bounds = [[UIScreen mainScreen] bounds];
        bounds.origin.y = -kStatusBarHeight - kTopBarHeight;
        
        _coverView = [[UIView alloc] initWithFrame:bounds];
        _coverView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
        [inView addSubview:_coverView];
        
        _coverView.userInteractionEnabled = YES;
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBgViewGesture:)];
        [_coverView addGestureRecognizer:tapGesture];
    }
    
    [self addSubview:self.titleView];
    [self addSubview:self.customView];
    
    //是否增加底部button
    if (self.bottomTitle || self.bottomIcon) {
        [self addSubview:self.bottomButton];
    }
    
    // 添加底部安全区域白色背景视图
    if (!_safeAreaBottomView) {
        _safeAreaBottomView = [[UIView alloc] init];
        _safeAreaBottomView.backgroundColor = [UIColor whiteColor];
    }
    [inView addSubview:_safeAreaBottomView];
    [inView addSubview:self];
    
    // 初始化底部安全区域视图的位置（在屏幕外）
    _safeAreaBottomView.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, kTabbarSafeBottomMargin);
    
    //显示
    [UIView animateWithDuration:.5 delay:0 usingSpringWithDamping:.8 initialSpringVelocity:0.5 options:(UIViewAnimationCurveEaseInOut|UIViewAnimationOptionBeginFromCurrentState) animations:^{
        
        self.frame = CGRectMake(0, kScreenHeight - _viewHeight - kTabbarSafeBottomMargin, SCREEN_WIDTH, _viewHeight);
        // 底部安全区域视图与弹窗底部对齐
        _safeAreaBottomView.frame = CGRectMake(0, kScreenHeight - kTabbarSafeBottomMargin, SCREEN_WIDTH, kTabbarSafeBottomMargin);
        _coverView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
        
    } completion:^(BOOL finished) {
        
    }];
}

- (void)br_updateViewConstrain {
    _viewHeight = kTitleViewDefaultHeight + _customHeight + _bottomButtonHeight;
    
    CGRect customViewFrame = self.customView.frame;
    customViewFrame.size.height = _customHeight;
    self.customView.frame = customViewFrame;

    self.bottomButton.frame = CGRectMake(0, _viewHeight - _bottomButtonHeight, SCREEN_WIDTH, _bottomButtonHeight);
    
    self.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, _viewHeight);
}
#pragma mark - Setter

- (void)setTitle:(NSString *)title {
    _title = title;
    
    _titleLabel.text = _title;
}

- (void)setTitleAttributedString:(NSAttributedString *)titleAttributedString {
    _titleAttributedString = titleAttributedString;
    
    _titleLabel.attributedText = titleAttributedString;
}

- (void)setCustomView:(UIView *)customView {
    UIView *oriCustomView = self.customView;
    
    _customView = customView;
    _customView.frame = CGRectMake(oriCustomView.frame.origin.x, oriCustomView.frame.origin.y, _customView.width, _customView.height);
    _customHeight = customView.height;
    
    [self br_updateViewConstrain];
}
- (void)setCustomHeight:(CGFloat)customHeight {
    _customHeight = customHeight;
    
    [self br_updateViewConstrain];
}

- (void)setBottomTitle:(NSString *)bottomTitle {
    _bottomTitle = bottomTitle;
    [self.bottomButton setTitle:_bottomTitle forState:UIControlStateNormal];
    _bottomButtonHeight = kTitleViewDefaultHeight;
    
    [self br_updateViewConstrain];
}

- (void)setBottomIcon:(UIImage *)bottomIcon {
    _bottomIcon = bottomIcon;
    [self.bottomButton setImage:bottomIcon forState:UIControlStateNormal];
    _bottomButtonHeight = kTitleViewDefaultHeight;
    
    [self br_updateViewConstrain];
}

- (void)setTopButtonTitle:(NSString *)topButtonTitle {
    _topButtonTitle = topButtonTitle;
    
    self.topButtonLabel.text = _topButtonTitle;
}

- (void)setTopButtonIcon:(UIImage *)topButtonIcon {
    _topButtonIcon = topButtonIcon;
    
    self.topButtonImageView.image = _topButtonIcon;
    
    //    [_titleView layoutIfNeeded];
    //
    //    [self.leftButtonImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
    //        make.size.mas_equalTo(leftButtonIcon.size).priorityHigh();
    //        make.left.mas_equalTo(@(10));
    //        make.centerY.equalTo(_titleView);
    //    }];
    
    CGSize size = _topButtonIcon.size;
//    _topButtonImageView.frame = CGRectMake(10, (45 - size.height) / 2.0, size.width, size.height);
}

- (void)setTopButtonColor:(UIColor *)topButtonColor {
    self.topButtonLabel.textColor = topButtonColor;
}

- (void)setButtons:(NSArray *)buttons {
    _buttons = buttons;
    
    [self setButtons:_buttons withSelfIndex:-1 withWithdrawMethod:NO];
}

- (void)setWithdrawMethodButtons:(NSArray *)buttons {
    _buttons = buttons;
    
    [self setButtons:_buttons withSelfIndex:-1 withWithdrawMethod:YES];
}

- (void)setIsBtnDeleteAble:(BOOL)isBtnDeleteAble {
    _isBtnDeleteAble = isBtnDeleteAble;
    
    if (isBtnDeleteAble) {
        self.toDeleteArray = [NSMutableArray arrayWithCapacity:0];
        
        if (self.topButtonLabel) {
            self.topButtonLabel.text = @"取消";
        }
        
        if (self.bottomButton) {
            [self.bottomButton setTitle:self.bottomDeleteTitle forState:UIControlStateNormal];
            [self.bottomButton setBackgroundImage:[Utils createImageWithColor:[UIColor br_textRedColor]] forState:UIControlStateNormal];
            [self.bottomButton setImage:nil forState:UIControlStateNormal];
            [self.bottomButton setTitle:@"确认删除" forState:UIControlStateNormal];
        }
        
    }
    else {
        
        if (self.topButtonLabel) {
            self.topButtonLabel.text = @"删除";
        }
        
        if (self.bottomButton) {
            [self.bottomButton setTitle:self.bottomTitle forState:UIControlStateNormal];
            [self.bottomButton setBackgroundImage:[Utils createImageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
            [self.bottomButton setImage:self.bottomIcon forState:UIControlStateNormal];
            [self.bottomButton setTitle:self.bottomTitle forState:UIControlStateNormal];
        }
    }
    
    
    //显示或者隐藏删除选项
    if (_buttonScrollView) {
        for (UIView* subView in _buttonScrollView.subviews) {
            if ([subView isKindOfClass:[UIButton class]]) {
                UIButton *button = (UIButton *)subView;
                UIButton *checkButton = [button viewWithTag:1001];
                if (checkButton && isBtnDeleteAble) {
                    checkButton.hidden = NO;
                }else {
                    checkButton.hidden = YES;
                }
            }
        }
    }
    else {
        for (UIView *subView in self.customView.subviews) {
            if ([subView isKindOfClass:[UIButton class]]) {
                UIButton *button = (UIButton *)subView;
                UIButton *checkButton = [button viewWithTag:1001];
                if (checkButton && isBtnDeleteAble) {
                    checkButton.hidden = NO;
                }
                else {
                    checkButton.hidden = YES;
                }
            }
        }
    }
}

- (void)setButtons:(NSArray *)buttons withSelfIndex:(NSInteger)index withWithdrawMethod:(BOOL)isWithdraw{
    _buttons = buttons;
    
    CGFloat buttonHeight = kCellDefaultHeight + 5;
    
    //添加按钮
    NSInteger count = buttons.count;
    CGFloat topSpace = 20.0f;
    
    //删掉之前所有按钮
    if (_buttonScrollView) {
        for (UIView *subView in _buttonScrollView.subviews) {
            if ([subView isKindOfClass:[UIButton class]]) {
                UIButton *button = (UIButton *)subView;
                [button removeFromSuperview];
            }
        }
    }
    else {
        for (UIView *subView in self.customView.subviews) {
            if ([subView isKindOfClass:[UIButton class]]) {
                UIButton *button = (UIButton *)subView;
                [button removeFromSuperview];
            }
        }
    }
    
    //设置最大高度
    if (count > kBottonsMaxHeightCount) {
        CGFloat height = kBottonsMaxHeightCount * buttonHeight + topSpace * 2;
        
        _buttonScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, height)];
        _buttonScrollView.contentSize = CGSizeMake(SCREEN_WIDTH, count * buttonHeight + topSpace * 2);
        
        [self.customView addSubview:_buttonScrollView];
        
        _customHeight = height;
    }
    else {
        _customHeight = buttonHeight * count + topSpace * 2;
    }
    
    for (int i = 0; i < count; i++) {
        
        NSString *btnTitle = @"";
        
        if(isWithdraw){
            BRCashTypeInfoModel *model = [buttons objectAtIndex:i];
            btnTitle = model.name;
        }else {
            btnTitle = [buttons objectAtIndex:i];
        }
//        NSString *btnTitle = [buttons objectAtIndex:i];
//        BRCashTypeInfoModel *model = [buttons objectAtIndex:i];
//        NSString *btnTitle = model.name;
        
        CGFloat titleWidth = [btnTitle widthForFont:kFontLight(17)];
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.frame = CGRectMake(0, buttonHeight * i + topSpace, SCREEN_WIDTH, buttonHeight);
        
        // 检查是否包含"【推荐】"，如果包含则使用富文本显示
        if ([btnTitle containsString:@"【推荐】"]) {
            NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:btnTitle];
            NSRange range = [btnTitle rangeOfString:@"【推荐】"];
            if (range.location != NSNotFound) {
                // 设置"【推荐】"为红色
                [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor br_textRedColor] range:range];
                // 设置其余文字为默认颜色
                NSRange fullRange = NSMakeRange(0, btnTitle.length);
                NSRange beforeRange = NSMakeRange(0, range.location);
                NSRange afterRange = NSMakeRange(range.location + range.length, btnTitle.length - (range.location + range.length));
                if (beforeRange.length > 0) {
                    [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor br_textDarkBlueColor] range:beforeRange];
                }
                if (afterRange.length > 0) {
                    [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor br_textDarkBlueColor] range:afterRange];
                }
                // 设置字体
                [attributedString addAttribute:NSFontAttributeName value:kFontLight(17) range:fullRange];
                [button setAttributedTitle:attributedString forState:UIControlStateNormal];
            }
        } else {
            [button setTitle:btnTitle forState:UIControlStateNormal];
            [button setTitleColor:[UIColor br_textDarkBlueColor] forState:UIControlStateNormal];
        }
        [[button titleLabel] setFont:kFontLight(17)];
        button.tag = i;
        [button addTarget:self action:@selector(clickActionButton:) forControlEvents:UIControlEventTouchUpInside];
        
        UILongPressGestureRecognizer *longPressGesture = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(longPressButton:)];
        longPressGesture.minimumPressDuration = 0.8;
        [button addGestureRecognizer:longPressGesture];
        
        if (isWithdraw) {
            BRCashTypeInfoModel *model = [buttons objectAtIndex:i];
//            if (i == 0) {
            if([model.type isEqualToString:@"weixin"]) {
                UIImage *yinlianImage = [UIImage imageNamed:@"wechat"];
                [button setImage:yinlianImage forState:UIControlStateNormal];
            }
//            else if (i == 1) {
            else if ([model.type isEqualToString:@"yinhangka"]){
                UIImage *wechatImage = [UIImage imageNamed:@"yinlian-icon"];
                [button setImage:wechatImage forState:UIControlStateNormal];
            }
            
            if(i == 0 && buttons.count > 1){
                UIImage *tuijianImage = [UIImage imageNamed:@"prescription_selfSupport"];
                UIImageView *tuijianImageView = [[UIImageView alloc] init];
                tuijianImageView.image = tuijianImage;
                [button addSubview:tuijianImageView];
                
                [tuijianImageView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(tuijianImage.size);
                    make.centerY.equalTo(button);
                    make.centerX.equalTo(button).with.offset(100);
                }];
            }
            
            [button setImageEdgeInsets:UIEdgeInsetsMake(0, -5, 0, 5)];
            [button setTitleEdgeInsets:UIEdgeInsetsMake(0, 5, 0, -5)];
        }
        else {
            [button setImage:nil forState:UIControlStateNormal];
            [button setTitleEdgeInsets:UIEdgeInsetsZero];
        }
        
        if (_buttonScrollView) {
            [_buttonScrollView addSubview:button];
        }
        else {
            [self.customView addSubview:button];
        }
        
        if (i == index - 1) {
            
            UIImage *myImage = [UIImage imageNamed:@"list_myself_icon"];
            UIImageView *myImageView = [[UIImageView alloc] initWithImage:myImage];
            [button addSubview:myImageView];
            
            CGFloat x =  titleWidth + (SCREEN_WIDTH - titleWidth) / 2.0 + 5;
            myImageView.frame = CGRectMake(x, (buttonHeight - myImage.size.height) / 2.0, myImage.size.width, myImage.size.height);
        }
        else {
            //删除框  不是自己
            UIImage *checkNoImage = [UIImage imageNamed:@"btn_check_no"];
            UIImage *checkImage = [UIImage imageNamed:@"btn_check_yes"];
            
            UIButton *checkButton = [UIButton buttonWithType:UIButtonTypeCustom];
            [checkButton setImage:checkImage forState:UIControlStateSelected];
            [checkButton setImage:checkNoImage forState:UIControlStateNormal];
            checkButton.userInteractionEnabled = NO;
            checkButton.tag = 1001;
            [button addSubview:checkButton];
            
            [checkButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.mas_equalTo(@10);
                make.size.mas_equalTo(checkNoImage.size);
                make.centerY.equalTo(button);
            }];
            
            checkButton.hidden = YES;
        }
    }
    
    [self br_updateViewConstrain];
    
}

#pragma mark - Click Event

- (void)clickCloseButton:(UIButton *)sender {
    [self close];
}

- (void)clickBottonButton:(UIButton *)sender {
    
    if (_clickBottomButtonCallBack) {
        _clickBottomButtonCallBack();
    }
}

- (void)longPressButton:(UITapGestureRecognizer *)sender {
    
    if (self.isBtnDeleteAble) {
        return;
    }
    
    if (sender.state == UIGestureRecognizerStateBegan) {
        NSLog(@"长按========%lu",sender.view.tag);
        
        if (self.clickLongPressButtonCallBack) {
            self.clickLongPressButtonCallBack(sender.view.tag);
        }
    }
}

- (void)clickActionButton:(UIButton *)sender {
    NSInteger tag = sender.tag;
    
    //如果可以删除
    if (self.isBtnDeleteAble) {
        
        //每次重新循环获取选择的按钮
        self.toDeleteArray = [NSMutableArray arrayWithCapacity:0];
        
        //选择删除按钮
        if (_buttonScrollView) {
            
            for (UIView *subView in _buttonScrollView.subviews) {
                if ([subView isKindOfClass:[UIButton class]]) {
                    UIButton *button = (UIButton *)subView;
                    UIButton *checkButton = (UIButton *)[button viewWithTag:1001];
                    if (checkButton && (checkButton.isHidden == NO)) {
                        
                        if (tag == button.tag) {
                            [checkButton setSelected:!checkButton.isSelected];
                        }
                        
                        
                        //如果选择
                        if (checkButton.isSelected) {
                            [self.toDeleteArray addObject:@(button.tag)];
                        }
                    }
                }
            }
        }
        else{
             
            for (UIView *subView in self.customView.subviews) {
                if ([subView isKindOfClass:[UIButton class]]) {
                    UIButton *button = (UIButton *)subView;
                    UIButton *checkButton = (UIButton *)[button viewWithTag:1001];
                    if (checkButton && (checkButton.hidden == NO)) {
                        
                        if (tag == button.tag) {
                            [checkButton setSelected:!checkButton.isSelected];
                        }
                        
                        //如果选择
                        if (checkButton.isSelected) {
                            [self.toDeleteArray addObject:@(button.tag)];
                        }
                    }
                }
            }
            
        }
    }
    else {
        if (_clickActionButtonCallBack) {
            _clickActionButtonCallBack(tag);
        }
    }
}

- (void)clickSelectedButton:(UIButton *)sender {
    
}
#pragma mark - TapGesture
- (void)tapBgViewGesture:(UITapGestureRecognizer *)sender {
    [self close];
}

#pragma mark -
- (void)close {
    
    if (_actionSheetViewClosed) {
        _actionSheetViewClosed();
    }
    
    [UIView animateWithDuration:.5 delay:0 usingSpringWithDamping:.8 initialSpringVelocity:0.5 options:(UIViewAnimationCurveEaseInOut|UIViewAnimationOptionBeginFromCurrentState) animations:^{
        
        self.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, _viewHeight);
        // 安全区域视图也移出屏幕
        if (_safeAreaBottomView) {
            _safeAreaBottomView.frame = CGRectMake(0, kScreenHeight, SCREEN_WIDTH, kTabbarSafeBottomMargin);
        }
        _coverView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0];
        
    }completion:^(BOOL finished) {
        
        [_coverView removeFromSuperview];
        _coverView = nil;
        // 清理底部安全区域视图
        if (_safeAreaBottomView) {
            [_safeAreaBottomView removeFromSuperview];
            _safeAreaBottomView = nil;
        }
        [self removeFromSuperview];
        
    }];
}

- (void)pressRightButton{}

@end
