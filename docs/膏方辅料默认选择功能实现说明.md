# 膏方辅料默认选择功能实现说明

## 功能需求
当剂型为膏方，并且用法用量中有辅料的时候，默认选择辅料选项中的第一个。

## 实现方案

### 修改文件
- `BRZY/Classes/Sections/BRPresUsageView.m`

### 修改内容
在 `updateAuxiliaryMaterialWithFactoryModel:` 方法中添加了默认选择第一个辅料的逻辑。

### 具体实现逻辑

1. **判断条件**：
   - 剂型必须是"膏方"
   - 辅料列表不为空且有数据

2. **选择逻辑**：
   - 从辅料列表中过滤掉"不添加辅料"选项
   - 如果过滤后还有辅料选项，则默认选择第一个
   - 如果只有"不添加辅料"选项，则不做默认选择

3. **界面更新**：
   - 将选中的辅料添加到 `selectedAuxiliaryMaterials` 数组
   - 更新界面显示文本为选中的辅料名称
   - 添加日志输出便于调试

### 代码实现

```objective-c
if (shouldShow) {
    // 当剂型为膏方，并且用法用量中有辅料的时候，默认选择辅料选项中的第一个
    if ([self.drugForm isEqualToString:@"膏方"] && self.auxiliaryMaterialList.count > 0) {
        // 分离辅料选项和"不添加辅料"选项
        NSMutableArray *materialOptions = [NSMutableArray array];
        for (NSString *option in self.auxiliaryMaterialList) {
            if (![option isEqualToString:@"不添加辅料"]) {
                [materialOptions addObject:option];
            }
        }
        
        // 如果有辅料选项，默认选择第一个
        if (materialOptions.count > 0) {
            NSString *firstMaterial = materialOptions.firstObject;
            [self.selectedAuxiliaryMaterials addObject:firstMaterial];
            [self updateAuxiliaryMaterialDisplayText:firstMaterial];
            NSLog(@"膏方剂型默认选择第一个辅料: %@", firstMaterial);
        } else {
            [self updateAuxiliaryMaterialDisplayText:@"请选择辅料"];
        }
    } else {
        [self updateAuxiliaryMaterialDisplayText:@"请选择辅料"];
    }
}
```

## 测试场景

已通过以下测试场景验证功能正确性：

1. **有辅料选项，不包含"不添加辅料"**
   - 期望：默认选择第一个辅料
   - 结果：✅ 通过

2. **有辅料选项，包含"不添加辅料"**
   - 期望：默认选择第一个非"不添加辅料"的选项
   - 结果：✅ 通过

3. **只有"不添加辅料"选项**
   - 期望：不做默认选择，显示"请选择辅料"
   - 结果：✅ 通过

4. **非膏方剂型**
   - 期望：不做默认选择
   - 结果：✅ 通过

## 影响范围

- 仅影响膏方剂型的辅料选择功能
- 不影响其他剂型的正常使用
- 不影响用户手动选择辅料的功能
- 保持与现有辅料选择弹窗的兼容性

## 注意事项

1. 默认选择只在初始化时生效，用户仍可以重新选择其他辅料
2. 如果辅料列表只包含"不添加辅料"，则不会做默认选择
3. 功能仅在膏方剂型下生效，其他剂型保持原有行为
4. 添加了日志输出，便于调试和问题排查

## 兼容性

- 与现有的辅料选择弹窗完全兼容
- 与处方保存和恢复功能兼容
- 与临时处方功能兼容
- 不影响现有的验证逻辑
