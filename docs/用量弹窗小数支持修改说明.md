# 用量弹窗小数支持修改说明

## 需求描述
开方界面的用法用量部分，对于水丸、蜜丸、膏方这三种剂型，点击弹出"选择用量"弹窗，当前弹窗有自定义按钮的时候，点击自定义，弹出自定义用量弹窗。需要支持输入1位小数，比如0.5、1.2这种输入。

## 修改内容

### 1. 键盘类型修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2698-2704行
**修改**: 根据剂型决定键盘类型，胶囊使用数字键盘，其他剂型使用小数键盘

```objc
// 修改前
inputTextField.keyboardType = UIKeyboardTypeNumberPad;

// 修改后
// 根据剂型决定键盘类型：胶囊只支持整数，其他剂型支持小数
if ([self.drugForm isEqualToString:@"胶囊"]) {
    inputTextField.keyboardType = UIKeyboardTypeNumberPad;  // 胶囊只支持整数
} else {
    inputTextField.keyboardType = UIKeyboardTypeDecimalPad;  // 水丸、蜜丸、膏方支持小数
}
```

### 2. 输入验证逻辑修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2826-2840行
**修改**: 根据剂型使用不同的验证方法，胶囊使用整数验证，其他剂型使用小数验证

```objc
// 修改前
// 限制输入长度为3位数字
if (inputText.length > 3) {
    textField.text = [inputText substringToIndex:3];
    inputText = textField.text;
}

// 只允许数字输入
NSCharacterSet *nonDigitSet = [[NSCharacterSet decimalDigitCharacterSet] invertedSet];
if ([inputText rangeOfCharacterFromSet:nonDigitSet].location != NSNotFound) {
    // 移除非数字字符
    textField.text = [[inputText componentsSeparatedByCharactersInSet:nonDigitSet] componentsJoinedByString:@""];
    inputText = textField.text;
}

// 修改后
// 根据剂型决定验证方式：胶囊只支持整数，其他剂型支持小数
NSString *validatedText;
if ([self.drugForm isEqualToString:@"胶囊"]) {
    validatedText = [self validateIntegerInput:inputText];  // 胶囊只支持整数
} else {
    validatedText = [self validateDecimalInput:inputText];  // 水丸、蜜丸、膏方支持小数
}

if (![validatedText isEqualToString:inputText]) {
    textField.text = validatedText;
    inputText = validatedText;
}
```

### 3. 重量计算逻辑修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2819行
**修改**: 将整数计算改为小数计算

```objc
// 修改前
int unitCount = [inputText intValue];

// 修改后
double unitCount = [inputText doubleValue];  // 改为支持小数
```

### 4. 属性类型修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.h`
**修改**: 将水丸、蜜丸、膏方的属性从 `NSInteger` 改为 `double` 类型，胶囊保持 `NSInteger` 类型

```objc
// 修改前
@property (nonatomic, assign) NSInteger selectedUnitCount; // 蜜丸
@property (nonatomic, assign) NSInteger selectedWaterPillUnitCount; // 水丸
@property (nonatomic, assign) NSInteger selectedCreamFormulaUnitCount; // 膏方
@property (nonatomic, assign) NSInteger selectedCapsuleUnitCount; // 胶囊

// 修改后
@property (nonatomic, assign) double selectedUnitCount; // 蜜丸支持小数
@property (nonatomic, assign) double selectedWaterPillUnitCount; // 水丸支持小数
@property (nonatomic, assign) double selectedCreamFormulaUnitCount; // 膏方支持小数
@property (nonatomic, assign) NSInteger selectedCapsuleUnitCount; // 胶囊保持整数
```

### 5. 显示更新方法修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**修改**: 更新各剂型的显示方法以支持小数显示

```objc
// 修改前（以蜜丸为例）
NSString *dosageText = [NSString stringWithFormat:@"%ld", (long)self.selectedUnitCount];

// 修改后
NSString *dosageText;
if (self.selectedUnitCount == (long)self.selectedUnitCount) {
    // 整数显示
    dosageText = [NSString stringWithFormat:@"%.0f", self.selectedUnitCount];
} else {
    // 小数显示，保留1位小数
    dosageText = [NSString stringWithFormat:@"%.1f", self.selectedUnitCount];
}
```

### 6. 保存按钮处理修改
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第2870-2890行
**修改**: 水丸、蜜丸、膏方支持小数，胶囊保持整数转换

```objc
// 修改前
self.selectedUnitCount = (NSInteger)inputValue;
self.selectedWaterPillUnitCount = (NSInteger)inputValue;
self.selectedCreamFormulaUnitCount = (NSInteger)inputValue;
self.selectedCapsuleUnitCount = (NSInteger)inputValue;

// 修改后
self.selectedUnitCount = inputValue;  // 蜜丸支持小数
self.selectedWaterPillUnitCount = inputValue;  // 水丸支持小数
self.selectedCreamFormulaUnitCount = inputValue;  // 膏方支持小数
self.selectedCapsuleUnitCount = (NSInteger)inputValue;  // 胶囊保持整数
```

### 7. 输入验证方法
**文件**: `BRZY/Classes/Sections/BRPresUsageView.m`
**位置**: 第3021-3103行
**新增**: 添加整数和小数输入验证方法

```objc
/**
 * 验证整数输入（用于胶囊剂型）
 * @param inputText 输入的文本
 * @return 验证后的有效文本
 */
- (NSString *)validateIntegerInput:(NSString *)inputText {
    // 只允许数字输入，限制总长度不超过3位（例如：999）
}

/**
 * 验证小数输入，支持1位小数（用于水丸、蜜丸、膏方剂型）
 * @param inputText 输入的文本
 * @return 验证后的有效文本
 */
- (NSString *)validateDecimalInput:(NSString *)inputText {
    // 支持格式：整数和1位小数，如：1、12、1.5、12.3
    // 限制总长度不超过4位（例如：99.9）
    // 使用正则表达式验证：^\\d{1,2}(\\.\\d{0,1})?$
}
```

### 8. 其他相关修改
**文件**: `BRZY/Classes/Sections/PrescriptionViewController.m`
**修改**: 更新处方恢复逻辑，水丸、蜜丸、膏方支持小数，胶囊保持整数

```objc
// 修改前
_usageView.selectedUnitCount = (NSInteger)round(packageCount);
_usageView.selectedWaterPillUnitCount = (NSInteger)round(packageCount);
_usageView.selectedCreamFormulaUnitCount = (NSInteger)round(packageCount);
_usageView.selectedCapsuleUnitCount = (NSInteger)round(packageCount);

// 修改后
_usageView.selectedUnitCount = packageCount;  // 蜜丸支持小数，不再四舍五入
_usageView.selectedWaterPillUnitCount = packageCount;  // 水丸支持小数，不再四舍五入
_usageView.selectedCreamFormulaUnitCount = packageCount;  // 膏方支持小数，不再四舍五入
_usageView.selectedCapsuleUnitCount = (NSInteger)round(packageCount);  // 胶囊保持整数四舍五入
```

## 影响范围
- ✅ 水丸剂型的自定义用量输入（支持小数）
- ✅ 蜜丸剂型的自定义用量输入（支持小数）
- ✅ 膏方剂型的自定义用量输入（支持小数）
- ❌ 胶囊剂型的自定义用量输入（仍然只支持整数）

## 测试建议
1. **水丸、蜜丸、膏方剂型**：
   - 测试用量选择弹窗的"自定义"按钮
   - 测试小数输入（如0.5、1.2、2.8等）
   - 测试边界情况（如99.9、0.1等）
   - 测试无效输入的处理（如多个小数点、超长输入等）
   - 测试整数输入仍然正常工作（如1、2、3等）

2. **胶囊剂型**：
   - 测试用量选择弹窗的"自定义"按钮
   - 确认只能输入整数（如3、6、9等）
   - 确认小数点无法输入（数字键盘）
   - 测试边界情况（如999等）

## 注意事项
- **水丸、蜜丸、膏方**：支持1位小数输入，总长度限制为4位字符（如99.9），整数部分最多2位数字
- **胶囊**：只支持整数输入，总长度限制为3位字符（如999）
- 保持了原有的样式和交互逻辑不变
- 根据剂型自动选择合适的键盘类型和验证逻辑
